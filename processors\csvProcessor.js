const Papa = require('papaparse');
const logger = require('../utils/logger');

class CSVProcessor {
    constructor() {
        this.name = 'CSV Processor';
    }

    async process(fileBuffer) {
        try {
            logger.info('Processing CSV file');
            
            const csvText = fileBuffer.toString('utf8');
            
            // Parse CSV with Papa Parse
            const parseResult = Papa.parse(csvText, {
                header: true,
                skipEmptyLines: true,
                transformHeader: (header) => header.trim().toLowerCase(),
                transform: (value) => value ? value.trim() : value
            });

            if (parseResult.errors.length > 0) {
                logger.warn('CSV parsing warnings:', parseResult.errors);
            }

            const rawData = parseResult.data;
            logger.info(`Parsed ${rawData.length} rows from CSV`);

            // Detect CSV format and normalize
            const normalizedTransactions = this.normalizeTransactions(rawData);
            
            return {
                success: true,
                transactions: normalizedTransactions,
                totalCount: normalizedTransactions.length,
                rawData: rawData.slice(0, 5), // Keep sample for debugging
                format: this.detectFormat(rawData[0] || {})
            };

        } catch (error) {
            logger.error('CSV processing failed:', error);
            throw new Error(`CSV processing failed: ${error.message}`);
        }
    }

    normalizeTransactions(rawData) {
        if (!rawData || rawData.length === 0) {
            return [];
        }

        const format = this.detectFormat(rawData[0]);
        logger.info(`Detected CSV format: ${format}`);

        return rawData.map((row, index) => {
            try {
                return this.normalizeTransaction(row, format, index);
            } catch (error) {
                logger.warn(`Failed to normalize row ${index}:`, error);
                return null;
            }
        }).filter(tx => tx !== null);
    }

    detectFormat(firstRow) {
        const headers = Object.keys(firstRow).map(h => h.toLowerCase());
        
        // Common bank formats
        if (headers.includes('transaction date') && headers.includes('description') && headers.includes('amount')) {
            return 'chase';
        }
        if (headers.includes('date') && headers.includes('description') && headers.includes('debit') && headers.includes('credit')) {
            return 'bank_of_america';
        }
        if (headers.includes('posting date') && headers.includes('description') && headers.includes('amount')) {
            return 'wells_fargo';
        }
        if (headers.includes('trans date') && headers.includes('description') && headers.includes('amount')) {
            return 'citi';
        }
        if (headers.includes('date') && headers.includes('payee') && headers.includes('amount')) {
            return 'quicken';
        }
        
        // Generic format detection
        const hasDate = headers.some(h => h.includes('date'));
        const hasDescription = headers.some(h => h.includes('description') || h.includes('payee') || h.includes('merchant'));
        const hasAmount = headers.some(h => h.includes('amount') || h.includes('debit') || h.includes('credit'));
        
        if (hasDate && hasDescription && hasAmount) {
            return 'generic';
        }
        
        return 'unknown';
    }

    normalizeTransaction(row, format, index) {
        let transaction = {
            originalIndex: index,
            date: null,
            description: '',
            amount: 0,
            type: 'expense',
            category: null,
            merchant: null,
            reference: null,
            balance: null
        };

        try {
            switch (format) {
                case 'chase':
                    transaction = this.parseChaseFormat(row);
                    break;
                case 'bank_of_america':
                    transaction = this.parseBankOfAmericaFormat(row);
                    break;
                case 'wells_fargo':
                    transaction = this.parseWellsFargoFormat(row);
                    break;
                case 'citi':
                    transaction = this.parseCitiFormat(row);
                    break;
                case 'quicken':
                    transaction = this.parseQuickenFormat(row);
                    break;
                case 'generic':
                    transaction = this.parseGenericFormat(row);
                    break;
                default:
                    transaction = this.parseGenericFormat(row);
            }

            // Validate required fields
            if (!transaction.date || !transaction.description || transaction.amount === 0) {
                throw new Error('Missing required fields');
            }

            // Additional processing
            transaction.originalIndex = index;
            transaction.description = this.cleanDescription(transaction.description);
            transaction.amount = this.parseAmount(transaction.amount);
            transaction.date = this.parseDate(transaction.date);

            return transaction;

        } catch (error) {
            logger.warn(`Failed to parse transaction at row ${index}:`, error);
            return null;
        }
    }

    parseChaseFormat(row) {
        return {
            date: row['transaction date'] || row['trans date'],
            description: row['description'] || row['memo'],
            amount: row['amount'],
            type: parseFloat(row['amount']) >= 0 ? 'income' : 'expense',
            category: row['category'],
            reference: row['check or slip #']
        };
    }

    parseBankOfAmericaFormat(row) {
        const debit = parseFloat(row['debit'] || 0);
        const credit = parseFloat(row['credit'] || 0);
        const amount = credit > 0 ? credit : -debit;
        
        return {
            date: row['date'],
            description: row['description'] || row['payee'],
            amount: amount,
            type: amount >= 0 ? 'income' : 'expense',
            reference: row['reference number'],
            balance: row['running balance']
        };
    }

    parseWellsFargoFormat(row) {
        return {
            date: row['posting date'] || row['date'],
            description: row['description'] || row['memo'],
            amount: row['amount'],
            type: parseFloat(row['amount']) >= 0 ? 'income' : 'expense',
            reference: row['reference number']
        };
    }

    parseCitiFormat(row) {
        return {
            date: row['trans date'] || row['date'],
            description: row['description'],
            amount: row['amount'] || row['debit'] || row['credit'],
            type: parseFloat(row['amount'] || row['credit'] || -row['debit']) >= 0 ? 'income' : 'expense'
        };
    }

    parseQuickenFormat(row) {
        return {
            date: row['date'],
            description: row['payee'] || row['description'],
            amount: row['amount'],
            type: parseFloat(row['amount']) >= 0 ? 'income' : 'expense',
            category: row['category'],
            merchant: row['payee']
        };
    }

    parseGenericFormat(row) {
        const headers = Object.keys(row);
        
        // Find date field
        const dateField = headers.find(h => 
            h.includes('date') || h.includes('trans') || h.includes('post')
        );
        
        // Find description field
        const descField = headers.find(h => 
            h.includes('description') || h.includes('payee') || 
            h.includes('merchant') || h.includes('memo')
        );
        
        // Find amount field
        const amountField = headers.find(h => 
            h.includes('amount') && !h.includes('balance')
        );
        
        // Handle debit/credit columns
        const debitField = headers.find(h => h.includes('debit'));
        const creditField = headers.find(h => h.includes('credit'));
        
        let amount = 0;
        if (amountField) {
            amount = row[amountField];
        } else if (debitField && creditField) {
            const debit = parseFloat(row[debitField] || 0);
            const credit = parseFloat(row[creditField] || 0);
            amount = credit > 0 ? credit : -debit;
        }
        
        return {
            date: row[dateField],
            description: row[descField] || 'Unknown Transaction',
            amount: amount,
            type: parseFloat(amount) >= 0 ? 'income' : 'expense',
            category: row['category'],
            merchant: row['merchant'] || row['payee']
        };
    }

    cleanDescription(description) {
        if (!description) return 'Unknown Transaction';
        
        return description
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\-\.\,\&]/g, '')
            .trim()
            .substring(0, 255);
    }

    parseAmount(amount) {
        if (typeof amount === 'number') return amount;
        if (!amount) return 0;
        
        // Remove currency symbols and commas
        const cleanAmount = amount.toString()
            .replace(/[$,\s]/g, '')
            .replace(/[()]/g, ''); // Remove parentheses
        
        const parsed = parseFloat(cleanAmount);
        return isNaN(parsed) ? 0 : parsed;
    }

    parseDate(dateStr) {
        if (!dateStr) return null;
        
        try {
            // Handle various date formats
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                // Try MM/DD/YYYY format
                const parts = dateStr.split('/');
                if (parts.length === 3) {
                    const [month, day, year] = parts;
                    return new Date(year, month - 1, day);
                }
                return null;
            }
            return date;
        } catch (error) {
            logger.warn(`Failed to parse date: ${dateStr}`);
            return null;
        }
    }
}

module.exports = CSVProcessor;