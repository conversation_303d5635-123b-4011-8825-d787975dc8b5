const express = require('express');
const { body, param } = require('express-validator');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const recommendationEngine = require('../services/recommendationEngine');
const notificationService = require('../services/notificationService');
const logger = require('../utils/logger');

const router = express.Router();

// Get active recommendations for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { type, priority, limit = 20 } = req.query;

        let query = `
            SELECT 
                r.*,
                g.title as goal_title
            FROM ai_recommendations r
            LEFT JOIN goals g ON r.goal_id = g.id
            WHERE r.user_id = $1 AND r.status = 'active'
            AND (r.expires_at IS NULL OR r.expires_at > NOW())
        `;

        const params = [userId];
        let paramIndex = 2;

        if (type) {
            query += ` AND r.type = $${paramIndex}`;
            params.push(type);
            paramIndex++;
        }

        if (priority) {
            const priorityMap = {
                'high': 5,
                'medium': 3,
                'low': 1
            };
            const priorityNumber = priorityMap[priority] || 3;
            query += ` AND r.priority = $${paramIndex}`;
            params.push(priorityNumber);
            paramIndex++;
        }

        query += ` ORDER BY r.priority DESC, r.created_at DESC LIMIT $${paramIndex}`;
        params.push(parseInt(limit));

        const result = await pool.query(query, params);
        
        const recommendations = result.rows.map(rec => ({
            ...rec,
            potential_savings: parseFloat(rec.potential_savings || 0),
            confidence_score: parseFloat(rec.confidence_score || 0),
            data_context: rec.data_context || {},
            goal_title: rec.goal_title || null
        }));

        res.json({ 
            recommendations,
            total: recommendations.length
        });
    } catch (error) {
        next(error);
    }
});

// Get recommendation history
router.get('/history', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { page = 1, limit = 20, status } = req.query;

        const offset = (page - 1) * limit;

        let query = `
            SELECT 
                r.*,
                g.title as goal_title,
                COUNT(ra.id) as action_count
            FROM ai_recommendations r
            LEFT JOIN goals g ON r.goal_id = g.id
            LEFT JOIN recommendation_actions ra ON r.id = ra.recommendation_id
            WHERE r.user_id = $1
        `;

        const params = [userId];
        let paramIndex = 2;

        if (status) {
            query += ` AND r.status = $${paramIndex}`;
            params.push(status);
            paramIndex++;
        }

        query += `
            GROUP BY r.id, g.title
            ORDER BY r.created_at DESC
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;
        params.push(limit, offset);

        const result = await pool.query(query, params);

        // Get total count
        let countQuery = 'SELECT COUNT(*) FROM ai_recommendations WHERE user_id = $1';
        const countParams = [userId];
        
        if (status) {
            countQuery += ' AND status = $2';
            countParams.push(status);
        }

        const countResult = await pool.query(countQuery, countParams);
        const total = parseInt(countResult.rows[0].count);

        const recommendations = result.rows.map(rec => ({
            ...rec,
            potential_savings: parseFloat(rec.potential_savings || 0),
            confidence_score: parseFloat(rec.confidence_score || 0),
            action_count: parseInt(rec.action_count)
        }));

        res.json({
            recommendations,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Dismiss recommendation
router.post('/:id/dismiss', authenticateToken, [
    param('id').isUUID().withMessage('Invalid recommendation ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const recommendationId = req.params.id;

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Update recommendation status
            const result = await client.query(
                'UPDATE ai_recommendations SET status = $1, updated_at = NOW() WHERE id = $2 AND user_id = $3 RETURNING *',
                ['dismissed', recommendationId, userId]
            );

            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Recommendation not found' });
            }

            // Record action
            await client.query(`
                INSERT INTO recommendation_actions (recommendation_id, user_id, action_type, action_data)
                VALUES ($1, $2, $3, $4)
            `, [
                recommendationId,
                userId,
                'dismissed',
                JSON.stringify({ timestamp: new Date().toISOString() })
            ]);

            await client.query('COMMIT');

            logger.info(`Recommendation dismissed: ${recommendationId} by user: ${userId}`);

            res.json({ 
                message: 'Recommendation dismissed successfully',
                recommendation: result.rows[0]
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    } catch (error) {
        next(error);
    }
});

// Mark recommendation as acted upon
router.post('/:id/act', authenticateToken, [
    param('id').isUUID().withMessage('Invalid recommendation ID'),
    body('action_details').optional().isString(),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const recommendationId = req.params.id;
        const { action_details } = req.body;

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Update recommendation status
            const result = await client.query(
                'UPDATE ai_recommendations SET status = $1, updated_at = NOW() WHERE id = $2 AND user_id = $3 RETURNING *',
                ['acted_upon', recommendationId, userId]
            );

            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Recommendation not found' });
            }

            // Record action
            await client.query(`
                INSERT INTO recommendation_actions (recommendation_id, user_id, action_type, action_data)
                VALUES ($1, $2, $3, $4)
            `, [
                recommendationId,
                userId,
                'acted_upon',
                JSON.stringify({ 
                    timestamp: new Date().toISOString(),
                    details: action_details
                })
            ]);

            await client.query('COMMIT');

            logger.info(`Recommendation acted upon: ${recommendationId} by user: ${userId}`);

            res.json({ 
                message: 'Recommendation marked as acted upon',
                recommendation: result.rows[0]
            });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    } catch (error) {
        next(error);
    }
});

// Provide feedback on recommendation
router.post('/:id/feedback', authenticateToken, [
    param('id').isUUID().withMessage('Invalid recommendation ID'),
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('feedback').optional().isString().isLength({ max: 500 }),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const recommendationId = req.params.id;
        const { rating, feedback } = req.body;

        // Verify recommendation exists and belongs to user
        const recCheck = await pool.query(
            'SELECT id FROM ai_recommendations WHERE id = $1 AND user_id = $2',
            [recommendationId, userId]
        );

        if (recCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Recommendation not found' });
        }

        // Record feedback action
        await pool.query(`
            INSERT INTO recommendation_actions (recommendation_id, user_id, action_type, action_data)
            VALUES ($1, $2, $3, $4)
        `, [
            recommendationId,
            userId,
            'feedback_given',
            JSON.stringify({ 
                rating,
                feedback,
                timestamp: new Date().toISOString()
            })
        ]);

        logger.info(`Feedback provided for recommendation: ${recommendationId} by user: ${userId}`);

        res.json({ 
            message: 'Feedback recorded successfully',
            rating,
            feedback
        });
    } catch (error) {
        next(error);
    }
});

// Force generate new recommendations
router.get('/generate', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await recommendationEngine.generateRecommendations(userId);

        // Create in-app notifications for new recommendations
        if (result.recommendations.length > 0) {
            await notificationService.sendBulkNotifications(result.recommendations, 'in_app');
        }

        logger.info(`Generated ${result.recommendations.length} recommendations for user: ${userId}`);

        res.json({
            message: `Generated ${result.recommendations.length} new recommendations`,
            recommendations: result.recommendations.map(rec => ({
                ...rec,
                potential_savings: parseFloat(rec.potential_savings || 0),
                confidence_score: parseFloat(rec.confidence_score || 0)
            })),
            limit_reached: result.limit_reached || false
        });
    } catch (error) {
        next(error);
    }
});

// Get recommendation effectiveness analytics (Premium)
router.get('/analytics', authenticateToken, requirePremium('advanced_analytics'), async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get recommendation statistics
        const statsQuery = `
            SELECT 
                COUNT(*) as total_recommendations,
                COUNT(CASE WHEN status = 'acted_upon' THEN 1 END) as acted_upon,
                COUNT(CASE WHEN status = 'dismissed' THEN 1 END) as dismissed,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
                AVG(confidence_score) as avg_confidence,
                SUM(potential_savings) as total_potential_savings,
                SUM(CASE WHEN status = 'acted_upon' THEN potential_savings ELSE 0 END) as realized_savings
            FROM ai_recommendations
            WHERE user_id = $1
            AND created_at >= CURRENT_DATE - INTERVAL '90 days'
        `;

        const statsResult = await pool.query(statsQuery, [userId]);
        const stats = statsResult.rows[0];

        // Get recommendation type breakdown
        const typeBreakdownQuery = `
            SELECT 
                type,
                COUNT(*) as count,
                COUNT(CASE WHEN status = 'acted_upon' THEN 1 END) as acted_upon_count,
                AVG(confidence_score) as avg_confidence,
                SUM(potential_savings) as total_potential_savings
            FROM ai_recommendations
            WHERE user_id = $1
            AND created_at >= CURRENT_DATE - INTERVAL '90 days'
            GROUP BY type
            ORDER BY count DESC
        `;

        const typeBreakdownResult = await pool.query(typeBreakdownQuery, [userId]);

        // Get feedback analytics
        const feedbackQuery = `
            SELECT 
                AVG(CAST(action_data->>'rating' AS INTEGER)) as avg_rating,
                COUNT(*) as feedback_count
            FROM recommendation_actions
            WHERE user_id = $1
            AND action_type = 'feedback_given'
            AND created_at >= CURRENT_DATE - INTERVAL '90 days'
        `;

        const feedbackResult = await pool.query(feedbackQuery, [userId]);

        res.json({
            period: '90 days',
            statistics: {
                total_recommendations: parseInt(stats.total_recommendations),
                acted_upon: parseInt(stats.acted_upon),
                dismissed: parseInt(stats.dismissed),
                active: parseInt(stats.active),
                action_rate: stats.total_recommendations > 0 ? 
                    (parseInt(stats.acted_upon) / parseInt(stats.total_recommendations)) * 100 : 0,
                avg_confidence: parseFloat(stats.avg_confidence || 0),
                total_potential_savings: parseFloat(stats.total_potential_savings || 0),
                realized_savings: parseFloat(stats.realized_savings || 0)
            },
            type_breakdown: typeBreakdownResult.rows.map(row => ({
                type: row.type,
                count: parseInt(row.count),
                acted_upon_count: parseInt(row.acted_upon_count),
                action_rate: row.count > 0 ? (parseInt(row.acted_upon_count) / parseInt(row.count)) * 100 : 0,
                avg_confidence: parseFloat(row.avg_confidence),
                total_potential_savings: parseFloat(row.total_potential_savings)
            })),
            feedback: {
                avg_rating: parseFloat(feedbackResult.rows[0].avg_rating || 0),
                feedback_count: parseInt(feedbackResult.rows[0].feedback_count || 0)
            }
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;

