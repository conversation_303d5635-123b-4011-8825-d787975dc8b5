const { Pool } = require('pg');
const logger = require('../utils/logger');

// Enhanced connection pool configuration
const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'finance_manager',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    
    // Connection pool settings
    max: 20, // Maximum number of connections in the pool
    min: 2,  // Minimum number of connections to maintain
    idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
    connectionTimeoutMillis: 10000, // 10 seconds to establish connection
    acquireTimeoutMillis: 10000, // 10 seconds to acquire connection from pool
    createTimeoutMillis: 10000, // 10 seconds to create new connection
    destroyTimeoutMillis: 5000,  // 5 seconds to destroy connection
    reapIntervalMillis: 1000,    // Check for idle connections every second
    createRetryIntervalMillis: 200, // Retry connection creation every 200ms
    maxUses: 7500, // Maximum number of times a connection can be used before being destroyed
    allowExitOnIdle: false, // Don't exit process when all connections are idle
    
    // Connection-level settings
    statement_timeout: 30000, // 30 seconds for query timeout
    query_timeout: 30000,     // 30 seconds for query timeout
    application_name: 'finance-manager-api',
    
    // SSL settings (adjust based on your database)
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    
    // Keep-alive settings to prevent connection drops
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000,
});

// Enhanced connection event handlers with proper error checking
pool.on('connect', (client) => {
    logger.info('New database connection established', {
        processId: client?.processID || 'unknown',
        totalConnections: pool.totalCount || 0,
        idleConnections: pool.idleCount || 0,
        waitingClients: pool.waitingCount || 0
    });
    
    // Set connection-level parameters
    client.query('SET statement_timeout = 30000');
    client.query('SET idle_in_transaction_session_timeout = 60000');
});

pool.on('acquire', (client) => {
    logger.debug('Connection acquired from pool', {
        processId: client?.processID || 'unknown',
        totalConnections: pool.totalCount || 0,
        idleConnections: pool.idleCount || 0,
        waitingClients: pool.waitingCount || 0
    });
});

pool.on('release', (client) => {
    // This is where the error was occurring - client might be undefined
    if (client && client.processID) {
        logger.debug('Connection released back to pool', {
            processId: client.processID,
            totalConnections: pool.totalCount || 0,
            idleConnections: pool.idleCount || 0,
            waitingClients: pool.waitingCount || 0
        });
    } else {
        logger.debug('Connection released back to pool (client undefined)', {
            totalConnections: pool.totalCount || 0,
            idleConnections: pool.idleCount || 0,
            waitingClients: pool.waitingCount || 0
        });
    }
});

pool.on('remove', (client) => {
    logger.info('Connection removed from pool', {
        processId: client?.processID || 'unknown',
        reason: 'Connection removed due to error or timeout',
        totalConnections: pool.totalCount || 0,
        idleConnections: pool.idleCount || 0,
        waitingClients: pool.waitingCount || 0
    });
});

pool.on('error', (err, client) => {
    logger.error('Database pool error:', {
        error: err.message,
        code: err.code,
        processId: client?.processID || 'unknown',
        totalConnections: pool.totalCount || 0,
        idleConnections: pool.idleCount || 0,
        waitingClients: pool.waitingCount || 0
    });
    
    // Don't exit the process on pool errors
    // The pool will handle reconnection automatically
});

// Enhanced connection function with retry logic and circuit breaker
const connectDB = async () => {
    let retryCount = 0;
    const maxRetries = 5;
    const retryDelay = 2000; // 2 seconds
    let lastError = null;

    while (retryCount < maxRetries) {
        try {
            // Test connection with timeout
            const client = await Promise.race([
                pool.connect(),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Connection timeout')), 10000)
                )
            ]);
            
            // Test the connection with a simple query
            const result = await Promise.race([
                client.query('SELECT NOW() as current_time, version() as db_version'),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Query timeout')), 5000)
                )
            ]);
            
            client.release();
            
            logger.info('Database connected successfully', {
                currentTime: result.rows[0].current_time,
                dbVersion: result.rows[0].db_version,
                poolStats: {
                    total: pool.totalCount || 0,
                    idle: pool.idleCount || 0,
                    waiting: pool.waitingCount || 0
                },
                retryAttempt: retryCount + 1
            });
            
            return pool;
            
        } catch (error) {
            retryCount++;
            lastError = error;
            
            logger.error(`Database connection failed (attempt ${retryCount}/${maxRetries}):`, {
                error: error.message,
                code: error.code,
                stack: error.stack,
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                database: process.env.DB_NAME
            });

            if (retryCount >= maxRetries) {
                logger.error('Max database connection retries exceeded');
                throw lastError;
            }

            // Wait before retrying with exponential backoff
            const waitTime = retryDelay * Math.pow(2, retryCount - 1);
            logger.info(`Retrying database connection in ${waitTime}ms...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
};

// Resilient query wrapper with automatic retry
const resilientQuery = async (text, params = [], maxRetries = 3) => {
    let retryCount = 0;
    let lastError = null;

    while (retryCount < maxRetries) {
        let client = null;
        try {
            // Get client with timeout
            client = await Promise.race([
                pool.connect(),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Connection acquisition timeout')), 5000)
                )
            ]);

            // Execute query with timeout
            const result = await Promise.race([
                client.query(text, params),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Query execution timeout')), 30000)
                )
            ]);

            client.release();
            return result;

        } catch (error) {
            if (client) {
                try {
                    client.release();
                } catch (releaseError) {
                    logger.warn('Failed to release client after error:', releaseError);
                }
            }

            retryCount++;
            lastError = error;

            // Check if error is retryable
            const isRetryableError = 
                error.code === 'ECONNRESET' ||
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.message.includes('Connection terminated') ||
                error.message.includes('timeout') ||
                error.message.includes('Connection acquisition timeout');

            if (!isRetryableError || retryCount >= maxRetries) {
                logger.error('Query failed after retries:', {
                    query: text.substring(0, 100),
                    error: error.message,
                    retryCount,
                    isRetryableError
                });
                throw lastError;
            }

            logger.warn(`Query failed, retrying (${retryCount}/${maxRetries}):`, {
                error: error.message,
                query: text.substring(0, 100)
            });

            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
    }

    throw lastError;
};

// Database migration function with enhanced error handling
const runMigrations = async () => {
    const client = await pool.connect();
    try {
        logger.info('Starting database migrations...');
        await client.query('BEGIN');
        
        // Create users table
        await client.query(`
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                subscription_tier VARCHAR(20) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
                subscription_expires_at TIMESTAMP,
                refresh_token TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create accounts table
        await client.query(`
            CREATE TABLE IF NOT EXISTS accounts (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(20) CHECK (type IN ('checking', 'savings', 'credit', 'investment', 'cash')),
                balance DECIMAL(12,2) DEFAULT 0,
                currency VARCHAR(3) DEFAULT 'USD',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create categories table
        await client.query(`
            CREATE TABLE IF NOT EXISTS categories (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                parent_id UUID REFERENCES categories(id),
                color VARCHAR(7),
                icon VARCHAR(50),
                is_default BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create transactions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS transactions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                category_id UUID REFERENCES categories(id),
                amount DECIMAL(12,2) NOT NULL,
                type VARCHAR(20) CHECK (type IN ('income', 'expense', 'transfer')),
                description TEXT DEFAULT '',
                transaction_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT NOW(),
                tags TEXT[],
                merchant VARCHAR(255),
                location VARCHAR(255),
                receipt_url VARCHAR(500),
                import_id UUID,
                original_description TEXT
            )
        `);

        // Create budgets table
        await client.query(`
            CREATE TABLE IF NOT EXISTS budgets (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                category_id UUID REFERENCES categories(id),
                amount DECIMAL(12,2) NOT NULL,
                period VARCHAR(20) CHECK (period IN ('weekly', 'monthly', 'yearly')),
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                alert_threshold DECIMAL(3,2) DEFAULT 0.8,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create bill_reminders table
        await client.query(`
            CREATE TABLE IF NOT EXISTS bill_reminders (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                amount DECIMAL(12,2),
                due_date DATE NOT NULL,
                frequency VARCHAR(20) CHECK (frequency IN ('once', 'weekly', 'monthly', 'yearly')),
                category_id UUID REFERENCES categories(id),
                is_paid BOOLEAN DEFAULT false,
                reminder_days_before INTEGER DEFAULT 3,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create account_shares table
        await client.query(`
            CREATE TABLE IF NOT EXISTS account_shares (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
                shared_with_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                permission_level VARCHAR(10) DEFAULT 'view' CHECK (permission_level IN ('view', 'edit')),
                shared_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create statement_uploads table
        await client.query(`
            CREATE TABLE IF NOT EXISTS statement_uploads (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_type VARCHAR(50) NOT NULL,
                file_size INTEGER NOT NULL,
                s3_key VARCHAR(500),
                status VARCHAR(50) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'parsed', 'categorized', 'ready', 'imported', 'failed')),
                raw_data JSONB,
                processed_data JSONB,
                error_message TEXT,
                total_transactions INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create import_sessions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS import_sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                upload_id UUID REFERENCES statement_uploads(id) ON DELETE CASCADE,
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                total_transactions INTEGER NOT NULL,
                imported_count INTEGER DEFAULT 0,
                skipped_count INTEGER DEFAULT 0,
                duplicate_count INTEGER DEFAULT 0,
                status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                settings JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                completed_at TIMESTAMP
            )
        `);

        // Create ai_categorization_cache table
        await client.query(`
            CREATE TABLE IF NOT EXISTS ai_categorization_cache (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                description_hash VARCHAR(64) UNIQUE NOT NULL,
                original_description TEXT NOT NULL,
                cleaned_description VARCHAR(100) NOT NULL,
                suggested_category VARCHAR(255),
                confidence_score DECIMAL(3,2),
                merchant VARCHAR(255),
                created_at TIMESTAMP DEFAULT NOW(),
                last_used TIMESTAMP DEFAULT NOW(),
                usage_count INTEGER DEFAULT 1
            )
        `);

        // Create goals table
        await client.query(`
            CREATE TABLE IF NOT EXISTS goals (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                target_amount DECIMAL(12,2) NOT NULL,
                current_amount DECIMAL(12,2) DEFAULT 0,
                target_date DATE NOT NULL,
                category VARCHAR(50) DEFAULT 'other' CHECK (category IN ('car', 'house', 'vacation', 'emergency', 'education', 'debt', 'other')),
                priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
                monthly_target DECIMAL(12,2),
                creation_method VARCHAR(20) DEFAULT 'manual' CHECK (creation_method IN ('manual', 'ai_assisted')),
                ai_strategy JSONB,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create goal_ai_sessions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS goal_ai_sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                goal_id UUID REFERENCES goals(id) ON DELETE SET NULL,
                conversation_state VARCHAR(50) DEFAULT 'goal_understanding' CHECK (conversation_state IN ('goal_understanding', 'data_verification', 'manual_input', 'strategy_creation', 'strategy_selection', 'completed')),
                conversation_data JSONB DEFAULT '[]'::jsonb,
                financial_analysis JSONB,
                strategies_presented JSONB,
                selected_strategy JSONB,
                session_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create goal_milestones table
        await client.query(`
            CREATE TABLE IF NOT EXISTS goal_milestones (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                goal_id UUID REFERENCES goals(id) ON DELETE CASCADE,
                title VARCHAR(200) NOT NULL,
                target_amount DECIMAL(12,2) NOT NULL,
                target_date DATE NOT NULL,
                achieved_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create goal_contributions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS goal_contributions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                goal_id UUID REFERENCES goals(id) ON DELETE CASCADE,
                transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
                amount DECIMAL(12,2) NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create ai_recommendations table
        await client.query(`
            CREATE TABLE IF NOT EXISTS ai_recommendations (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                type VARCHAR(50) NOT NULL CHECK (type IN ('goal_focused', 'budget_optimization', 'behavioral_insight', 'proactive_alert', 'opportunity_alert')),
                category VARCHAR(50),
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                action_text VARCHAR(100),
                potential_savings DECIMAL(10,2),
                confidence_score DECIMAL(3,2) CHECK (confidence_score BETWEEN 0 AND 1),
                priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
                data_context JSONB,
                goal_id UUID REFERENCES goals(id),
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'dismissed', 'acted_upon', 'expired')),
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create notifications table
        await client.query(`
            CREATE TABLE IF NOT EXISTS notifications (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                recommendation_id UUID REFERENCES ai_recommendations(id) ON DELETE CASCADE,
                type VARCHAR(30) NOT NULL CHECK (type IN ('push', 'in_app', 'email')),
                title VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                action_data JSONB,
                delivery_channel VARCHAR(20) CHECK (delivery_channel IN ('mobile_push', 'web_push', 'in_app', 'email')),
                sent_at TIMESTAMP,
                read_at TIMESTAMP,
                clicked_at TIMESTAMP,
                status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'read', 'clicked', 'failed')),
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create notification_preferences table
        await client.query(`
            CREATE TABLE IF NOT EXISTS notification_preferences (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
                push_notifications BOOLEAN DEFAULT true,
                in_app_notifications BOOLEAN DEFAULT true,
                email_notifications BOOLEAN DEFAULT true,
                frequency VARCHAR(20) DEFAULT 'smart' CHECK (frequency IN ('immediate', 'daily', 'weekly', 'smart')),
                quiet_hours_start TIME DEFAULT '22:00',
                quiet_hours_end TIME DEFAULT '08:00',
                goal_reminders BOOLEAN DEFAULT true,
                budget_alerts BOOLEAN DEFAULT true,
                insight_notifications BOOLEAN DEFAULT true,
                promotional_notifications BOOLEAN DEFAULT false,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create recommendation_actions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS recommendation_actions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                recommendation_id UUID REFERENCES ai_recommendations(id) ON DELETE CASCADE,
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                action_type VARCHAR(50) CHECK (action_type IN ('viewed', 'dismissed', 'acted_upon', 'feedback_given')),
                action_data JSONB,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create indexes for performance
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_import_id ON transactions(import_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_statement_uploads_user_id ON statement_uploads(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_statement_uploads_status ON statement_uploads(status)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_import_sessions_user_id ON import_sessions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_cache_hash ON ai_categorization_cache(description_hash)');
        
        // Goal-related indexes
        await client.query('CREATE INDEX IF NOT EXISTS idx_goals_user_id ON goals(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_goal_ai_sessions_user_id ON goal_ai_sessions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_goal_ai_sessions_active ON goal_ai_sessions(session_active)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_goal_milestones_goal_id ON goal_milestones(goal_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_goal_contributions_goal_id ON goal_contributions(goal_id)');

        // Recommendation and notification indexes
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_recommendations_user_id ON ai_recommendations(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(type)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_recommendations_priority ON ai_recommendations(priority)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_recommendation_actions_user_id ON recommendation_actions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_recommendation_actions_rec_id ON recommendation_actions(recommendation_id)');

        await client.query('COMMIT');
        logger.info('Database migrations completed successfully');
    } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Migration failed:', error);
        throw error;
    } finally {
        client.release();
    }
};

// Enhanced database health check function
const checkDatabaseHealth = async () => {
    try {
        const client = await pool.connect();
        const startTime = Date.now();
        
        // Test basic connectivity
        const basicTest = await client.query('SELECT NOW() as current_time');
        const basicLatency = Date.now() - startTime;
        
        // Test a simple table query
        const tableTestStart = Date.now();
        const tableTest = await client.query('SELECT COUNT(*) as user_count FROM users');
        const tableLatency = Date.now() - tableTestStart;
        
        client.release();
        
        return {
            status: 'healthy',
            timestamp: basicTest.rows[0].current_time,
            latency: {
                basic_query: basicLatency,
                table_query: tableLatency
            },
            connection_pool: {
                total: pool.totalCount || 0,
                idle: pool.idleCount || 0,
                waiting: pool.waitingCount || 0
            },
            database_info: {
                user_count: parseInt(tableTest.rows[0].user_count)
            }
        };
    } catch (error) {
        logger.error('Database health check failed:', error);
        return {
            status: 'unhealthy',
            error: error.message,
            connection_pool: {
                total: pool.totalCount || 0,
                idle: pool.idleCount || 0,
                waiting: pool.waitingCount || 0
            }
        };
    }
};

// Graceful shutdown function
const gracefulShutdown = async () => {
    logger.info('Shutting down database connections...');
    try {
        await pool.end();
        logger.info('Database connections closed successfully');
    } catch (error) {
        logger.error('Error during database shutdown:', error);
    }
};

// Handle process termination
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

module.exports = {
    pool,
    connectDB,
    runMigrations,
    checkDatabaseHealth,
    gracefulShutdown,
    resilientQuery
};
