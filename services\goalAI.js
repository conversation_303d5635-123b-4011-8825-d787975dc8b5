const { CohereClientV2 } = require('cohere-ai');
const { pool } = require('../config/database');
const financialAnalyzer = require('./financialAnalyzer');
const logger = require('../utils/logger');

class GoalAI {
    constructor() {
        this.cohere = new CohereClientV2({
            token: process.env.COHERE_API_KEY
        });

        this.goalCategories = {
            car: {
                name: 'Car Purchase',
                icon: '🚗',
                typical_amounts: [500000, 800000, 1200000, 1500000, 2500000],
                avg_timeline_months: 18,
                additional_costs_factor: 1.15,
                tips: ['Consider total cost of ownership', 'Factor in insurance and maintenance']
            },
            house: {
                name: 'House/Property',
                icon: '🏠',
                typical_amounts: [2500000, 5000000, 8000000, 12000000],
                avg_timeline_months: 60,
                additional_costs_factor: 1.25,
                tips: ['Down payment is typically 20%', 'Factor in registration and legal costs']
            },
            vacation: {
                name: 'Vacation/Travel',
                icon: '✈️',
                typical_amounts: [50000, 100000, 200000, 500000],
                avg_timeline_months: 6
            },
            emergency: {
                name: 'Emergency Fund',
                icon: '🛡️',
                target_months: [3, 6, 12],
                avg_timeline_months: 12
            },
            education: {
                name: 'Education',
                icon: '🎓',
                typical_amounts: [200000, 500000, 1000000, 2500000],
                avg_timeline_months: 24
            },
            other: {
                name: 'Other Goal',
                icon: '🎯'
            }
        };

        this.conversationStates = {
            GOAL_UNDERSTANDING: 'goal_understanding',
            DATA_VERIFICATION: 'data_verification',
            MANUAL_INPUT: 'manual_input',
            STRATEGY_CREATION: 'strategy_creation',
            STRATEGY_SELECTION: 'strategy_selection',
            COMPLETED: 'completed'
        };
    }

    // Start new AI goal setting session
    async startGoalSession(userId, initialGoal) {
        try {
            logger.info(`Starting AI goal session for user: ${userId}`);

            const sessionId = require('uuid').v4();
            
            // Create session record
            await pool.query(`
                INSERT INTO goal_ai_sessions (
                    id, user_id, conversation_state, conversation_data
                ) VALUES ($1, $2, $3, $4)
            `, [
                sessionId,
                userId,
                this.conversationStates.GOAL_UNDERSTANDING,
                JSON.stringify([{
                    role: 'user',
                    message: initialGoal,
                    timestamp: new Date().toISOString()
                }])
            ]);

            // Process initial goal understanding
            const response = await this.processGoalUnderstanding(sessionId, userId, initialGoal);

            logger.info(`AI goal session started: ${sessionId}`);
            return { sessionId, response };

        } catch (error) {
            logger.error('Failed to start goal session:', error);
            throw error;
        }
    }

    // Continue conversation in existing session
    async continueConversation(sessionId, userId, userMessage) {
        try {
            logger.info(`Continuing conversation for session: ${sessionId}`);

            // Get session data
            const sessionResult = await pool.query(
                'SELECT * FROM goal_ai_sessions WHERE id = $1 AND user_id = $2 AND session_active = true',
                [sessionId, userId]
            );

            if (sessionResult.rows.length === 0) {
                throw new Error('Session not found or inactive');
            }

            const session = sessionResult.rows[0];
            const conversationData = session.conversation_data || [];

            // Add user message to conversation
            conversationData.push({
                role: 'user',
                message: userMessage,
                timestamp: new Date().toISOString()
            });

            // Process based on current state
            let response;
            switch (session.conversation_state) {
                case this.conversationStates.GOAL_UNDERSTANDING:
                    response = await this.processGoalUnderstanding(sessionId, userId, userMessage, conversationData);
                    break;
                case this.conversationStates.DATA_VERIFICATION:
                    response = await this.processDataVerification(sessionId, userId, userMessage, conversationData);
                    break;
                case this.conversationStates.MANUAL_INPUT:
                    response = await this.processManualInput(sessionId, userId, userMessage, conversationData);
                    break;
                case this.conversationStates.STRATEGY_CREATION:
                    response = await this.processStrategyCreation(sessionId, userId, userMessage, conversationData);
                    break;
                case this.conversationStates.STRATEGY_SELECTION:
                    response = await this.processStrategySelection(sessionId, userId, userMessage, conversationData);
                    break;
                default:
                    throw new Error('Invalid conversation state');
            }

            return response;

        } catch (error) {
            logger.error('Failed to continue conversation:', error);
            throw error;
        }
    }

    // Process goal understanding phase
    async processGoalUnderstanding(sessionId, userId, userMessage, conversationData = []) {
        try {
            // Extract goal details using AI
            const goalExtraction = await this.extractGoalDetails(userMessage);
            
            // Build financial profile
            const financialProfile = await financialAnalyzer.buildFinancialProfile(userId);

            // Add AI response to conversation
            conversationData.push({
                role: 'assistant',
                message: goalExtraction.response,
                timestamp: new Date().toISOString(),
                extracted_data: goalExtraction.extracted
            });

            // Determine next state based on data completeness
            let nextState = this.conversationStates.DATA_VERIFICATION;
            if (financialProfile.data_completeness.score < 0.3) {
                nextState = this.conversationStates.MANUAL_INPUT;
            }

            // Update session
            await pool.query(`
                UPDATE goal_ai_sessions 
                SET 
                    conversation_state = $1,
                    conversation_data = $2,
                    financial_analysis = $3,
                    updated_at = NOW()
                WHERE id = $4
            `, [
                nextState,
                JSON.stringify(conversationData),
                JSON.stringify(financialProfile),
                sessionId
            ]);

            return {
                message: goalExtraction.response,
                state: nextState,
                extracted_goal: goalExtraction.extracted,
                financial_profile: financialProfile,
                next_action: this.getNextActionMessage(nextState, financialProfile)
            };

        } catch (error) {
            logger.error('Failed to process goal understanding:', error);
            throw error;
        }
    }

    // Process data verification phase
    async processDataVerification(sessionId, userId, userMessage, conversationData) {
        try {
            // Get session data
            const sessionResult = await pool.query(
                'SELECT financial_analysis FROM goal_ai_sessions WHERE id = $1',
                [sessionId]
            );

            const financialProfile = sessionResult.rows[0].financial_analysis;

            // Verify data with user
            const verification = await this.verifyFinancialData(userMessage, financialProfile);

            conversationData.push({
                role: 'assistant',
                message: verification.response,
                timestamp: new Date().toISOString()
            });

            const nextState = verification.data_confirmed 
                ? this.conversationStates.STRATEGY_CREATION 
                : this.conversationStates.MANUAL_INPUT;

            await pool.query(`
                UPDATE goal_ai_sessions 
                SET 
                    conversation_state = $1,
                    conversation_data = $2,
                    updated_at = NOW()
                WHERE id = $3
            `, [nextState, JSON.stringify(conversationData), sessionId]);

            return {
                message: verification.response,
                state: nextState,
                data_confirmed: verification.data_confirmed,
                next_action: this.getNextActionMessage(nextState, financialProfile)
            };

        } catch (error) {
            logger.error('Failed to process data verification:', error);
            throw error;
        }
    }

    // Process manual input phase
    async processManualInput(sessionId, userId, userMessage, conversationData) {
        try {
            // Parse manual financial input
            const manualData = await this.parseManualFinancialInput(userMessage);

            conversationData.push({
                role: 'assistant',
                message: manualData.response,
                timestamp: new Date().toISOString()
            });

            // Update financial analysis with manual data
            const sessionResult = await pool.query(
                'SELECT financial_analysis FROM goal_ai_sessions WHERE id = $1',
                [sessionId]
            );

            let financialProfile = sessionResult.rows[0].financial_analysis || {};
            financialProfile = { ...financialProfile, ...manualData.financial_data };

            const nextState = manualData.data_complete 
                ? this.conversationStates.STRATEGY_CREATION 
                : this.conversationStates.MANUAL_INPUT;

            await pool.query(`
                UPDATE goal_ai_sessions 
                SET 
                    conversation_state = $1,
                    conversation_data = $2,
                    financial_analysis = $3,
                    updated_at = NOW()
                WHERE id = $4
            `, [nextState, JSON.stringify(conversationData), JSON.stringify(financialProfile), sessionId]);

            return {
                message: manualData.response,
                state: nextState,
                financial_data: manualData.financial_data,
                data_complete: manualData.data_complete,
                next_action: this.getNextActionMessage(nextState, financialProfile)
            };

        } catch (error) {
            logger.error('Failed to process manual input:', error);
            throw error;
        }
    }

    // Process strategy creation phase
    async processStrategyCreation(sessionId, userId, userMessage, conversationData) {
        try {
            // Get session data
            const sessionResult = await pool.query(
                'SELECT financial_analysis, conversation_data FROM goal_ai_sessions WHERE id = $1',
                [sessionId]
            );

            const financialProfile = sessionResult.rows[0].financial_analysis;
            const fullConversation = sessionResult.rows[0].conversation_data;

            // Extract goal details from conversation
            const goalDetails = this.extractGoalFromConversation(fullConversation);

            // Generate strategies
            const strategies = await this.generateStrategies(goalDetails, financialProfile);

            conversationData.push({
                role: 'assistant',
                message: strategies.response,
                timestamp: new Date().toISOString()
            });

            await pool.query(`
                UPDATE goal_ai_sessions 
                SET 
                    conversation_state = $1,
                    conversation_data = $2,
                    strategies_presented = $3,
                    updated_at = NOW()
                WHERE id = $4
            `, [
                this.conversationStates.STRATEGY_SELECTION,
                JSON.stringify(conversationData),
                JSON.stringify(strategies.strategies),
                sessionId
            ]);

            return {
                message: strategies.response,
                state: this.conversationStates.STRATEGY_SELECTION,
                strategies: strategies.strategies,
                next_action: 'Please select your preferred strategy by number (1, 2, or 3)'
            };

        } catch (error) {
            logger.error('Failed to process strategy creation:', error);
            throw error;
        }
    }

    // Process strategy selection phase
    async processStrategySelection(sessionId, userId, userMessage, conversationData) {
        try {
            // Get strategies
            const sessionResult = await pool.query(
                'SELECT strategies_presented FROM goal_ai_sessions WHERE id = $1',
                [sessionId]
            );

            const strategies = sessionResult.rows[0].strategies_presented;
            const selectedStrategy = this.parseStrategySelection(userMessage, strategies);

            conversationData.push({
                role: 'assistant',
                message: selectedStrategy.response,
                timestamp: new Date().toISOString()
            });

            await pool.query(`
                UPDATE goal_ai_sessions 
                SET 
                    conversation_state = $1,
                    conversation_data = $2,
                    selected_strategy = $3,
                    updated_at = NOW()
                WHERE id = $4
            `, [
                this.conversationStates.COMPLETED,
                JSON.stringify(conversationData),
                JSON.stringify(selectedStrategy.strategy),
                sessionId
            ]);

            return {
                message: selectedStrategy.response,
                state: this.conversationStates.COMPLETED,
                selected_strategy: selectedStrategy.strategy,
                next_action: 'Your goal is ready to be created! Use the finalize endpoint to create your goal.'
            };

        } catch (error) {
            logger.error('Failed to process strategy selection:', error);
            throw error;
        }
    }

    // Extract goal details using AI
    async extractGoalDetails(goalStatement) {
        try {
            const prompt = `
Analyze this financial goal statement and extract key details:
"${goalStatement}"

Extract and return JSON with:
{
  "goal_type": "car|house|vacation|emergency|education|other",
  "target_amount": number or null,
  "timeline": "number of months" or null,
  "description": "cleaned goal description",
  "confidence": 0.1-1.0,
  "questions": ["clarifying questions if needed"]
}

Focus on Indian financial context. If amount is unclear, ask for clarification.
`;

            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a financial advisor helping users set savings goals. Extract goal details accurately and ask clarifying questions when needed.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 500
            });

            const content = response.message.content[0].text.trim();
            const extracted = JSON.parse(content);

            // Generate response message
            let responseMessage = `I understand you want to save for ${extracted.description}. `;
            
            if (extracted.target_amount) {
                responseMessage += `Your target amount is ₹${extracted.target_amount.toLocaleString('en-IN')}. `;
            }
            
            if (extracted.timeline) {
                responseMessage += `You want to achieve this in ${extracted.timeline} months. `;
            }

            if (extracted.questions && extracted.questions.length > 0) {
                responseMessage += `\n\nTo help you better, I need to know: ${extracted.questions.join(' ')}`;
            } else {
                responseMessage += `\n\nLet me analyze your financial situation to create the best savings strategy for you.`;
            }

            return {
                extracted: extracted,
                response: responseMessage
            };

        } catch (error) {
            logger.error('Failed to extract goal details:', error);
            return {
                extracted: {
                    goal_type: 'other',
                    target_amount: null,
                    timeline: null,
                    description: goalStatement,
                    confidence: 0.3,
                    questions: ['Could you please specify the target amount and timeline for your goal?']
                },
                response: 'I understand you have a financial goal. Could you please provide more details about the target amount and timeline?'
            };
        }
    }

    // Verify financial data with user
    async verifyFinancialData(userMessage, financialProfile) {
        try {
            const prompt = `
Based on the user's transaction data, I estimated:
- Monthly Income: ₹${financialProfile.income.estimated_monthly.toLocaleString('en-IN')}
- Monthly Expenses: ₹${financialProfile.expenses.total_monthly.toLocaleString('en-IN')}
- Monthly Savings: ₹${financialProfile.savings.average_monthly.toLocaleString('en-IN')}

User response: "${userMessage}"

Determine if the user confirms this data is accurate. Return JSON:
{
  "data_confirmed": true/false,
  "corrections": {"income": number, "expenses": number} or null,
  "response": "conversational response"
}
`;

            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 300
            });

            const content = response.message.content[0].text.trim();
            return JSON.parse(content);

        } catch (error) {
            logger.error('Failed to verify financial data:', error);
            return {
                data_confirmed: false,
                corrections: null,
                response: 'I need to gather your financial information manually. Could you please tell me your monthly income and expenses?'
            };
        }
    }

    // Parse manual financial input
    async parseManualFinancialInput(userMessage) {
        try {
            const prompt = `
Parse financial information from user input:
"${userMessage}"

Extract and return JSON:
{
  "financial_data": {
    "monthly_income": number or null,
    "monthly_expenses": number or null,
    "monthly_debt": number or null
  },
  "data_complete": true/false,
  "response": "conversational response asking for missing info or confirming data"
}

Look for income, expenses, EMI/debt payments in Indian context.
`;

            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 400
            });

            const content = response.message.content[0].text.trim();
            return JSON.parse(content);

        } catch (error) {
            logger.error('Failed to parse manual input:', error);
            return {
                financial_data: {},
                data_complete: false,
                response: 'Could you please provide your monthly income and expenses in a clear format? For example: "My monthly income is ₹50,000 and expenses are ₹35,000"'
            };
        }
    }

    // Generate savings strategies
    async generateStrategies(goalDetails, financialProfile) {
        try {
            const monthlyIncome = financialProfile.income?.estimated_monthly || financialProfile.monthly_income || 0;
            const monthlyExpenses = financialProfile.expenses?.total_monthly || financialProfile.monthly_expenses || 0;
            const currentSavings = monthlyIncome - monthlyExpenses;

            const prompt = `
Create 3 realistic savings strategies for this goal:
Goal: ${goalDetails.description}
Target: ₹${goalDetails.target_amount?.toLocaleString('en-IN')}
Timeline: ${goalDetails.timeline} months
Monthly Income: ₹${monthlyIncome.toLocaleString('en-IN')}
Monthly Expenses: ₹${monthlyExpenses.toLocaleString('en-IN')}
Current Savings: ₹${currentSavings.toLocaleString('en-IN')}

Return JSON with 3 strategies:
{
  "strategies": [
    {
      "name": "Strategy name",
      "monthly_savings_needed": number,
      "approach": "expense_reduction|income_increase|timeline_adjustment|hybrid",
      "description": "detailed explanation",
      "feasibility": "high|medium|low",
      "specific_actions": ["action1", "action2"],
      "timeline_months": number
    }
  ],
  "response": "conversational explanation of all strategies"
}

Make strategies realistic for Indian financial context.
`;

            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a financial advisor creating practical savings strategies for Indian users.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 800
            });

            const content = response.message.content[0].text.trim();
            return JSON.parse(content);

        } catch (error) {
            logger.error('Failed to generate strategies:', error);
            throw error;
        }
    }

    // Parse strategy selection
    parseStrategySelection(userMessage, strategies) {
        try {
            // Look for strategy number in user message
            const match = userMessage.match(/\b([123])\b/);
            const strategyIndex = match ? parseInt(match[1]) - 1 : 0;

            if (strategyIndex >= 0 && strategyIndex < strategies.length) {
                const selectedStrategy = strategies[strategyIndex];
                return {
                    strategy: selectedStrategy,
                    response: `Perfect! You've selected "${selectedStrategy.name}". This strategy requires saving ₹${selectedStrategy.monthly_savings_needed.toLocaleString('en-IN')} per month. I'll create your goal with this strategy.`
                };
            } else {
                return {
                    strategy: strategies[0], // Default to first strategy
                    response: `I'll go with the first strategy for you. You can always modify your goal later.`
                };
            }

        } catch (error) {
            logger.error('Failed to parse strategy selection:', error);
            return {
                strategy: strategies[0],
                response: 'I\'ll set up the first strategy for you.'
            };
        }
    }

    // Extract goal details from conversation history
    extractGoalFromConversation(conversationData) {
        // Find the extracted goal data from the conversation
        for (const message of conversationData) {
            if (message.extracted_data) {
                return message.extracted_data;
            }
        }

        // Fallback: parse from first user message
        return {
            goal_type: 'other',
            target_amount: 100000,
            timeline: 12,
            description: 'Financial Goal'
        };
    }

    // Get next action message based on state
    getNextActionMessage(state, financialProfile) {
        switch (state) {
            case this.conversationStates.DATA_VERIFICATION:
                return 'Please confirm if this financial analysis is accurate, or let me know what needs to be corrected.';
            case this.conversationStates.MANUAL_INPUT:
                return 'Please provide your monthly income and expenses so I can create a personalized strategy.';
            case this.conversationStates.STRATEGY_CREATION:
                return 'Let me analyze your finances and create savings strategies for you.';
            case this.conversationStates.STRATEGY_SELECTION:
                return 'Please select your preferred strategy by number.';
            case this.conversationStates.COMPLETED:
                return 'Your goal is ready to be created!';
            default:
                return 'Continue the conversation to set up your goal.';
        }
    }

    // Finalize goal creation from completed session
    async finalizeGoal(sessionId, userId) {
        try {
            logger.info(`Finalizing goal for session: ${sessionId}`);

            // Get completed session data
            const sessionResult = await pool.query(
                'SELECT * FROM goal_ai_sessions WHERE id = $1 AND user_id = $2 AND conversation_state = $3',
                [sessionId, userId, this.conversationStates.COMPLETED]
            );

            if (sessionResult.rows.length === 0) {
                throw new Error('Session not found or not completed');
            }

            const session = sessionResult.rows[0];
            const goalDetails = this.extractGoalFromConversation(session.conversation_data);
            const selectedStrategy = session.selected_strategy;

            // Calculate monthly target
            const monthlyTarget = selectedStrategy.monthly_savings_needed || 
                (goalDetails.target_amount / goalDetails.timeline);

            // Create goal
            const goalResult = await pool.query(`
                INSERT INTO goals (
                    user_id, title, description, target_amount, target_date,
                    category, monthly_target, creation_method, ai_strategy
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            `, [
                userId,
                goalDetails.description,
                `AI-assisted goal: ${goalDetails.description}`,
                goalDetails.target_amount,
                new Date(Date.now() + goalDetails.timeline * 30 * 24 * 60 * 60 * 1000), // Approximate months to date
                goalDetails.goal_type,
                monthlyTarget,
                'ai_assisted',
                JSON.stringify({
                    strategy: selectedStrategy,
                    financial_analysis: session.financial_analysis,
                    conversation_summary: this.summarizeConversation(session.conversation_data)
                })
            ]);

            const goal = goalResult.rows[0];

            // Create milestones
            await this.createGoalMilestones(goal.id, goalDetails.target_amount, goalDetails.timeline);

            // Update session with goal ID
            await pool.query(
                'UPDATE goal_ai_sessions SET goal_id = $1, session_active = false WHERE id = $2',
                [goal.id, sessionId]
            );

            logger.info(`Goal created successfully: ${goal.id} from session: ${sessionId}`);

            return {
                goal: goal,
                strategy: selectedStrategy,
                message: `🎉 Your goal "${goalDetails.description}" has been created! You need to save ₹${monthlyTarget.toLocaleString('en-IN')} per month to reach your target.`
            };

        } catch (error) {
            logger.error('Failed to finalize goal:', error);
            throw error;
        }
    }

    // Create goal milestones
    async createGoalMilestones(goalId, targetAmount, timelineMonths) {
        const milestones = [];
        const milestoneCount = Math.min(timelineMonths, 4); // Max 4 milestones

        for (let i = 1; i <= milestoneCount; i++) {
            const percentage = i / milestoneCount;
            const milestoneAmount = targetAmount * percentage;
            const milestoneDate = new Date(Date.now() + (timelineMonths * percentage) * 30 * 24 * 60 * 60 * 1000);

            await pool.query(`
                INSERT INTO goal_milestones (goal_id, title, target_amount, target_date)
                VALUES ($1, $2, $3, $4)
            `, [
                goalId,
                `Milestone ${i}: ${Math.round(percentage * 100)}% Complete`,
                milestoneAmount,
                milestoneDate
            ]);
        }
    }

    // Summarize conversation for storage
    summarizeConversation(conversationData) {
        return {
            total_messages: conversationData.length,
            duration_minutes: this.calculateConversationDuration(conversationData),
            key_points: this.extractKeyPoints(conversationData)
        };
    }

    calculateConversationDuration(conversationData) {
        if (conversationData.length < 2) return 0;
        const start = new Date(conversationData[0].timestamp);
        const end = new Date(conversationData[conversationData.length - 1].timestamp);
        return Math.round((end - start) / (1000 * 60)); // Minutes
    }

    extractKeyPoints(conversationData) {
        return conversationData
            .filter(msg => msg.role === 'user')
            .map(msg => msg.message.substring(0, 100))
            .slice(0, 3); // Keep first 3 user messages as key points
    }
}

module.exports = new GoalAI();