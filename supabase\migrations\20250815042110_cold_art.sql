/*
  # Update Goal Priority System

  1. Schema Changes
    - Modify `goals` table priority column from INTEGER to VARCHAR
    - Update constraint to accept 'high', 'medium', 'low' values
    - Convert existing numeric priorities to categorical equivalents

  2. Data Migration
    - Convert existing priority values: 5,4 → 'high', 3 → 'medium', 2,1 → 'low'
    - Preserve all existing goal data

  3. Application Updates
    - Update validation rules to accept new priority values
    - Maintain backward compatibility during transition
*/

-- Step 1: Add new priority column with categorical values
ALTER TABLE goals ADD COLUMN priority_category VARCHAR(10);

-- Step 2: Migrate existing data
UPDATE goals SET priority_category = 
  CASE 
    WHEN priority >= 4 THEN 'high'
    WHEN priority = 3 THEN 'medium'
    WHEN priority <= 2 THEN 'low'
    ELSE 'medium'
  END;

-- Step 3: Make the new column NOT NULL
ALTER TABLE goals ALTER COLUMN priority_category SET NOT NULL;

-- Step 4: Add constraint for valid values
ALTER TABLE goals ADD CONSTRAINT goals_priority_category_check 
  CHECK (priority_category IN ('high', 'medium', 'low'));

-- Step 5: Drop old priority column and constraint
ALTER TABLE goals DROP CONSTRAINT IF EXISTS goals_priority_check;
ALTER TABLE goals DROP COLUMN priority;

-- Step 6: Rename new column to priority
ALTER TABLE goals RENAME COLUMN priority_category TO priority;

-- Step 7: Update default value
ALTER TABLE goals ALTER COLUMN priority SET DEFAULT 'medium';

-- Step 8: Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_goals_priority ON goals(priority);