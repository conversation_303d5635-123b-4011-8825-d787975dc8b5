const { connectDB } = require('../config/database');
const databaseMonitor = require('../utils/databaseMonitor');
const logger = require('../utils/logger');

async function startWithMonitoring() {
    try {
        logger.info('Starting application with enhanced database monitoring...');

        // Connect to database
        await connectDB();
        logger.info('Database connection established');

        // Start database monitoring
        databaseMonitor.start();
        logger.info('Database monitoring started');

        // Start the main server
        require('../server');

    } catch (error) {
        logger.error('Failed to start application with monitoring:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down monitoring...');
    databaseMonitor.stop();
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down monitoring...');
    databaseMonitor.stop();
});

// Start if this script is run directly
if (require.main === module) {
    startWithMonitoring();
}

module.exports = startWithMonitoring;