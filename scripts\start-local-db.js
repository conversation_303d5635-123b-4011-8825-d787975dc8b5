const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function startLocalDatabase() {
    try {
        console.log('🐳 Starting local PostgreSQL and Redis containers...');
        
        // Check if Dock<PERSON> is running
        try {
            await execAsync('docker --version');
            console.log('✅ Docker is available');
        } catch (error) {
            console.error('❌ Docker is not installed or not running');
            console.log('Please install Docker Desktop from: https://www.docker.com/products/docker-desktop');
            process.exit(1);
        }

        // Check if Docker daemon is running
        try {
            await execAsync('docker info', { timeout: 5000 });
            console.log('✅ Docker daemon is running');
        } catch (error) {
            console.error('❌ Docker daemon is not running');
            console.log('Please start Docker Desktop and try again');
            process.exit(1);
        }

        // Stop any existing containers first
        console.log('🧹 Cleaning up any existing containers...');
        try {
            await execAsync('docker-compose down', { timeout: 10000 });
        } catch (error) {
            // Ignore errors if no containers exist
        }

        // Start the containers with verbose output
        console.log('🚀 Starting containers...');
        const startProcess = spawn('docker-compose', ['up', '-d'], {
            stdio: 'inherit'
        });

        await new Promise((resolve, reject) => {
            startProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`Docker Compose exited with code ${code}`));
                }
            });
            
            startProcess.on('error', (error) => {
                reject(error);
            });
        });
        
        console.log('⏳ Waiting for databases to be ready...');
        
        // Simple wait approach - check if containers are running
        let attempts = 0;
        const maxAttempts = 30;
        
        while (attempts < maxAttempts) {
            try {
                // Check if containers are running
                const { stdout } = await execAsync('docker-compose ps --format "table {{.Name}}\\t{{.State}}"');
                console.log('\n📊 Container Status:');
                console.log(stdout);
                
                // Check if both containers are running
                if (stdout.includes('finance-manager-postgres') && stdout.includes('finance-manager-redis')) {
                    const lines = stdout.split('\n');
                    const postgresRunning = lines.some(line => line.includes('finance-manager-postgres') && line.includes('running'));
                    const redisRunning = lines.some(line => line.includes('finance-manager-redis') && line.includes('running'));
                    
                    if (postgresRunning && redisRunning) {
                        console.log('✅ Both containers are running!');
                        
                        // Wait a bit more for services to be fully ready
                        console.log('⏳ Waiting for services to initialize...');
                        await new Promise(resolve => setTimeout(resolve, 5000));
                        break;
                    }
                }
                
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 2000));
                process.stdout.write('.');
                
            } catch (error) {
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 2000));
                process.stdout.write('.');
            }
        }
        
        if (attempts >= maxAttempts) {
            console.log('\n⚠️  Containers may still be starting. You can check manually with: docker-compose ps');
        }
        
        // Test connections
        console.log('\n🔍 Testing database connections...');
        
        // Test PostgreSQL
        try {
            await execAsync('docker exec finance-manager-postgres pg_isready -U postgres', { timeout: 5000 });
            console.log('✅ PostgreSQL is ready');
        } catch (error) {
            console.log('⚠️  PostgreSQL may still be starting up');
        }
        
        // Test Redis
        try {
            await execAsync('docker exec finance-manager-redis redis-cli ping', { timeout: 5000 });
            console.log('✅ Redis is ready');
        } catch (error) {
            console.log('⚠️  Redis may still be starting up');
        }
        
        console.log('\n🎯 Connection Details:');
        console.log('PostgreSQL: localhost:5432');
        console.log('Database: finance_manager');
        console.log('Username: postgres');
        console.log('Password: password123');
        console.log('Redis: localhost:6379');
        
        console.log('\n📝 Next Steps:');
        console.log('1. Copy .env.local to .env: cp .env.local .env');
        console.log('2. Run migrations: npm run db:migrate');
        console.log('3. Seed test data: npm run db:seed');
        console.log('4. Start the application: npm run dev');
        
        console.log('\n🛑 To stop containers: npm run db:stop');
        console.log('🔄 To reset database: npm run db:reset');
        
    } catch (error) {
        console.error('\n❌ Failed to start local database:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Make sure Docker Desktop is running');
        console.log('2. Check if ports 5432 and 6379 are available');
        console.log('3. Try: docker-compose down && docker-compose up -d');
        console.log('4. Check logs: docker-compose logs');
        process.exit(1);
    }
}

startLocalDatabase();