const logger = require('../utils/logger');

class OFXProcessor {
    constructor() {
        this.name = 'OFX Processor';
    }

    async process(fileBuffer) {
        try {
            logger.info('Processing OFX file');
            
            const ofxText = fileBuffer.toString('utf8');
            
            // Parse OFX data using custom parser
            const ofxData = this.parseOFX(ofxText);
            
            if (!ofxData) {
                throw new Error('Failed to parse OFX data');
            }
            
            // Extract transactions
            const transactions = this.extractTransactions(ofxData);
            
            return {
                success: true,
                transactions: transactions,
                totalCount: transactions.length,
                rawData: {
                    bankId: this.extractBankId(ofxData),
                    accountId: this.extractAccountId(ofxData),
                    accountType: this.extractAccountType(ofxData),
                    currency: this.extractCurrency(ofxData)
                }
            };

        } catch (error) {
            logger.error('OFX processing failed:', error);
            throw new Error(`OFX processing failed: ${error.message}`);
        }
    }

    // Custom OFX parser - more reliable than external packages
    parseOFX(ofxText) {
        try {
            // Clean up the OFX text
            let cleanText = ofxText
                .replace(/^\s*OFXHEADER:.*$/gm, '')
                .replace(/^\s*DATA:.*$/gm, '')
                .replace(/^\s*VERSION:.*$/gm, '')
                .replace(/^\s*SECURITY:.*$/gm, '')
                .replace(/^\s*ENCODING:.*$/gm, '')
                .replace(/^\s*CHARSET:.*$/gm, '')
                .replace(/^\s*COMPRESSION:.*$/gm, '')
                .replace(/^\s*OLDFILEUID:.*$/gm, '')
                .replace(/^\s*NEWFILEUID:.*$/gm, '')
                .trim();

            // Convert OFX to a more parseable format
            const parsed = this.parseOFXTags(cleanText);
            return parsed;
        } catch (error) {
            logger.error('OFX parsing error:', error);
            throw error;
        }
    }

    parseOFXTags(text) {
        const result = {};
        const stack = [result];
        let current = result;
        
        // Simple regex-based parser for OFX tags
        const tagRegex = /<([^/>]+)>([^<]*)/g;
        const closeTagRegex = /<\/([^>]+)>/g;
        
        let match;
        let position = 0;
        
        while ((match = tagRegex.exec(text)) !== null) {
            const [fullMatch, tagName, content] = match;
            
            // Check if there's a closing tag before the next opening tag
            const nextOpenTag = tagRegex.lastIndex;
            const closeTagMatch = closeTagRegex.exec(text.substring(position));
            
            if (content.trim()) {
                // Leaf node with content
                current[tagName] = content.trim();
            } else {
                // Container node
                current[tagName] = {};
                stack.push(current[tagName]);
                current = current[tagName];
            }
            
            position = nextOpenTag;
        }
        
        return result;
    }

    extractTransactions(ofxData) {
        const transactions = [];
        
        try {
            // Navigate OFX structure to find transactions
            const bankStatement = this.findBankStatement(ofxData);
            
            if (!bankStatement) {
                throw new Error('No bank statement found in OFX');
            }
            
            const banktranlist = bankStatement.BANKTRANLIST;
            if (!banktranlist) {
                throw new Error('No transaction list found in OFX');
            }
            
            // Handle both single transaction and array of transactions
            const stmtTransactions = this.extractTransactionList(banktranlist);
            
            stmtTransactions.forEach((txn, index) => {
                try {
                    const transaction = this.normalizeOFXTransaction(txn, index);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                } catch (error) {
                    logger.warn(`Failed to process OFX transaction ${index}:`, error);
                }
            });
            
            logger.info(`Extracted ${transactions.length} transactions from OFX`);
            
        } catch (error) {
            logger.error('Failed to extract transactions from OFX:', error);
            throw error;
        }
        
        return transactions;
    }

    findBankStatement(ofxData) {
        // Try different possible paths to find the bank statement
        const possiblePaths = [
            ofxData.OFX?.BANKMSGSRSV1?.STMTTRNRS?.STMTRS,
            ofxData.OFX?.BANKMSGSRS?.STMTTRNRS?.STMTRS,
            ofxData.OFX?.BANKMSGSRS?.STMTRS,
            ofxData.BANKMSGSRSV1?.STMTTRNRS?.STMTRS,
            ofxData.BANKMSGSRS?.STMTTRNRS?.STMTRS,
            ofxData.BANKMSGSRS?.STMTRS
        ];

        for (const path of possiblePaths) {
            if (path) {
                return path;
            }
        }

        return null;
    }

    extractTransactionList(banktranlist) {
        // Handle different structures for transaction lists
        if (banktranlist.STMTTRN) {
            if (Array.isArray(banktranlist.STMTTRN)) {
                return banktranlist.STMTTRN;
            } else {
                return [banktranlist.STMTTRN];
            }
        }

        // Try to find transactions in other possible structures
        const transactions = [];
        for (const key in banktranlist) {
            if (key.startsWith('STMTTRN') || key === 'TRN') {
                const txn = banktranlist[key];
                if (Array.isArray(txn)) {
                    transactions.push(...txn);
                } else {
                    transactions.push(txn);
                }
            }
        }

        return transactions;
    }

    normalizeOFXTransaction(txn, index) {
        try {
            // Extract transaction data with fallbacks
            const trnType = txn.TRNTYPE || txn.TYPE;
            const dtPosted = txn.DTPOSTED || txn.DATE;
            const trnAmt = parseFloat(txn.TRNAMT || txn.AMOUNT || 0);
            const fitId = txn.FITID || txn.ID;
            const name = txn.NAME || txn.PAYEE || '';
            const memo = txn.MEMO || txn.DESCRIPTION || '';
            
            // Determine transaction type
            let type = 'expense';
            if (trnAmt > 0) {
                type = 'income';
            } else if (trnType === 'TRANSFER' || trnType === 'XFER') {
                type = 'transfer';
            }
            
            // Parse date
            const date = this.parseOFXDate(dtPosted);
            if (!date) {
                throw new Error('Invalid transaction date');
            }
            
            // Build description
            let description = name;
            if (memo && memo !== name) {
                description += memo ? ` - ${memo}` : '';
            }
            description = description.trim() || 'OFX Transaction';
            
            return {
                originalIndex: index,
                date: date,
                description: this.cleanDescription(description),
                amount: Math.abs(trnAmt), // Store as positive, type indicates direction
                type: type,
                category: null,
                merchant: name || null,
                reference: fitId,
                ofxType: trnType
            };
            
        } catch (error) {
            logger.warn(`Failed to normalize OFX transaction:`, error);
            return null;
        }
    }

    parseOFXDate(dateStr) {
        if (!dateStr) return null;
        
        try {
            // OFX dates are typically in YYYYMMDD or YYYYMMDDHHMMSS format
            const dateOnly = dateStr.toString().substring(0, 8);
            
            if (dateOnly.length !== 8) {
                throw new Error('Invalid OFX date format');
            }
            
            const year = parseInt(dateOnly.substring(0, 4));
            const month = parseInt(dateOnly.substring(4, 6)) - 1; // Month is 0-based
            const day = parseInt(dateOnly.substring(6, 8));
            
            const date = new Date(year, month, day);
            
            if (isNaN(date.getTime())) {
                throw new Error('Invalid date values');
            }
            
            return date;
            
        } catch (error) {
            logger.warn(`Failed to parse OFX date: ${dateStr}`, error);
            return null;
        }
    }

    extractBankId(ofxData) {
        try {
            const stmtrs = this.findBankStatement(ofxData);
            return stmtrs?.BANKACCTFROM?.BANKID || null;
        } catch (error) {
            return null;
        }
    }

    extractAccountId(ofxData) {
        try {
            const stmtrs = this.findBankStatement(ofxData);
            return stmtrs?.BANKACCTFROM?.ACCTID || null;
        } catch (error) {
            return null;
        }
    }

    extractAccountType(ofxData) {
        try {
            const stmtrs = this.findBankStatement(ofxData);
            return stmtrs?.BANKACCTFROM?.ACCTTYPE || null;
        } catch (error) {
            return null;
        }
    }

    extractCurrency(ofxData) {
        try {
            const stmtrs = this.findBankStatement(ofxData);
            return stmtrs?.CURDEF || 'USD';
        } catch (error) {
            return 'USD';
        }
    }

    cleanDescription(description) {
        if (!description) return 'OFX Transaction';
        
        return description
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\-\.\,\&]/g, '')
            .trim()
            .substring(0, 255);
    }
}

module.exports = OFXProcessor;