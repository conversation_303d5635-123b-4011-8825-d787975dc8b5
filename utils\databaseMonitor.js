const { pool, checkDatabaseHealth } = require('../config/database');
const logger = require('./logger');
const cron = require('cron');

class DatabaseMonitor {
    constructor() {
        this.isMonitoring = false;
        this.healthCheckInterval = null;
        this.alertThresholds = {
            latency: 1000, // 1 second
            connectionPoolUtilization: 0.8, // 80%
            waitingClients: 5
        };
        this.consecutiveFailures = 0;
        this.maxConsecutiveFailures = 3;
    }

    start() {
        if (this.isMonitoring) {
            logger.warn('Database monitor is already running');
            return;
        }

        this.isMonitoring = true;
        
        // Health check every 30 seconds
        this.healthCheckInterval = setInterval(async () => {
            await this.performHealthCheck();
        }, 30000);

        // Detailed monitoring every 5 minutes
        const detailedMonitorJob = new cron.CronJob('*/5 * * * *', async () => {
            await this.performDetailedMonitoring();
        });

        // Connection pool cleanup every hour
        const cleanupJob = new cron.CronJob('0 * * * *', async () => {
            await this.performPoolCleanup();
        });

        detailedMonitorJob.start();
        cleanupJob.start();

        logger.info('Database monitor started');
    }

    stop() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        
        this.isMonitoring = false;
        logger.info('Database monitor stopped');
    }

    async performHealthCheck() {
        try {
            const health = await checkDatabaseHealth();
            
            if (health.status === 'healthy') {
                this.consecutiveFailures = 0;
                
                // Check for performance issues
                if (health.latency.basic_query > this.alertThresholds.latency) {
                    logger.warn('Database latency alert', {
                        latency: health.latency.basic_query,
                        threshold: this.alertThresholds.latency
                    });
                }

                // Check connection pool utilization
                const poolUtilization = (health.connection_pool.total - health.connection_pool.idle) / health.connection_pool.total;
                if (poolUtilization > this.alertThresholds.connectionPoolUtilization) {
                    logger.warn('High connection pool utilization', {
                        utilization: poolUtilization,
                        threshold: this.alertThresholds.connectionPoolUtilization,
                        pool_stats: health.connection_pool
                    });
                }

                // Check waiting clients
                if (health.connection_pool.waiting > this.alertThresholds.waitingClients) {
                    logger.warn('High number of waiting clients', {
                        waiting: health.connection_pool.waiting,
                        threshold: this.alertThresholds.waitingClients
                    });
                }

            } else {
                this.consecutiveFailures++;
                logger.error('Database health check failed', {
                    consecutive_failures: this.consecutiveFailures,
                    health_status: health
                });

                if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                    logger.error('CRITICAL: Database has failed consecutive health checks', {
                        consecutive_failures: this.consecutiveFailures,
                        max_failures: this.maxConsecutiveFailures
                    });
                    
                    // Could trigger alerts here (email, Slack, etc.)
                    await this.triggerCriticalAlert(health);
                }
            }

        } catch (error) {
            this.consecutiveFailures++;
            logger.error('Health check execution failed', {
                error: error.message,
                consecutive_failures: this.consecutiveFailures
            });
        }
    }

    async performDetailedMonitoring() {
        try {
            logger.info('Performing detailed database monitoring');

            // Get connection pool statistics
            const poolStats = {
                total: pool.totalCount,
                idle: pool.idleCount,
                waiting: pool.waitingCount
            };

            // Get database size and table statistics
            const dbStats = await this.getDatabaseStatistics();

            // Get slow query information (if available)
            const slowQueries = await this.getSlowQueries();

            // Log comprehensive monitoring data
            logger.info('Database monitoring report', {
                pool_statistics: poolStats,
                database_statistics: dbStats,
                slow_queries: slowQueries,
                timestamp: new Date().toISOString()
            });

            // Check for potential issues
            await this.analyzePerformanceMetrics(poolStats, dbStats);

        } catch (error) {
            logger.error('Detailed monitoring failed', error);
        }
    }

    async getDatabaseStatistics() {
        try {
            const client = await pool.connect();
            
            // Get database size
            const dbSizeQuery = `
                SELECT pg_size_pretty(pg_database_size(current_database())) as database_size,
                       pg_database_size(current_database()) as database_size_bytes
            `;
            const dbSizeResult = await client.query(dbSizeQuery);

            // Get table sizes
            const tableSizeQuery = `
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                LIMIT 10
            `;
            const tableSizeResult = await client.query(tableSizeQuery);

            // Get connection count
            const connectionQuery = `
                SELECT count(*) as active_connections
                FROM pg_stat_activity 
                WHERE state = 'active'
            `;
            const connectionResult = await client.query(connectionQuery);

            client.release();

            return {
                database_size: dbSizeResult.rows[0],
                table_sizes: tableSizeResult.rows,
                active_connections: parseInt(connectionResult.rows[0].active_connections)
            };

        } catch (error) {
            logger.error('Failed to get database statistics', error);
            return null;
        }
    }

    async getSlowQueries() {
        try {
            const client = await pool.connect();
            
            // Get currently running queries (if pg_stat_statements is available)
            const slowQueryQuery = `
                SELECT 
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows
                FROM pg_stat_statements 
                WHERE mean_time > 1000  -- Queries taking more than 1 second on average
                ORDER BY mean_time DESC 
                LIMIT 5
            `;
            
            try {
                const result = await client.query(slowQueryQuery);
                client.release();
                return result.rows;
            } catch (error) {
                // pg_stat_statements might not be enabled
                client.release();
                return [];
            }

        } catch (error) {
            logger.error('Failed to get slow queries', error);
            return [];
        }
    }

    async analyzePerformanceMetrics(poolStats, dbStats) {
        // Analyze connection pool efficiency
        if (poolStats.total > 0) {
            const utilizationRate = (poolStats.total - poolStats.idle) / poolStats.total;
            
            if (utilizationRate > 0.9) {
                logger.warn('Very high connection pool utilization detected', {
                    utilization_rate: utilizationRate,
                    recommendation: 'Consider increasing max pool size or optimizing query performance'
                });
            }
        }

        // Analyze database growth
        if (dbStats && dbStats.database_size) {
            const sizeInMB = dbStats.database_size.database_size_bytes / (1024 * 1024);
            
            if (sizeInMB > 1000) { // 1GB
                logger.info('Large database detected', {
                    size_mb: sizeInMB,
                    recommendation: 'Consider implementing data archiving or partitioning strategies'
                });
            }
        }

        // Analyze table sizes
        if (dbStats && dbStats.table_sizes) {
            const largestTable = dbStats.table_sizes[0];
            if (largestTable && largestTable.size_bytes > 100 * 1024 * 1024) { // 100MB
                logger.info('Large table detected', {
                    table: largestTable.tablename,
                    size: largestTable.size,
                    recommendation: 'Monitor query performance on this table'
                });
            }
        }
    }

    async performPoolCleanup() {
        try {
            logger.info('Performing connection pool cleanup');
            
            // Log current pool state
            const beforeStats = {
                total: pool.totalCount,
                idle: pool.idleCount,
                waiting: pool.waitingCount
            };

            // The pg pool automatically manages connections, but we can log the state
            logger.info('Connection pool state before cleanup', beforeStats);

            // Force cleanup of idle connections (if needed)
            // Note: pg pool handles this automatically, but we could implement custom logic here

            const afterStats = {
                total: pool.totalCount,
                idle: pool.idleCount,
                waiting: pool.waitingCount
            };

            logger.info('Connection pool cleanup completed', {
                before: beforeStats,
                after: afterStats
            });

        } catch (error) {
            logger.error('Pool cleanup failed', error);
        }
    }

    async triggerCriticalAlert(healthStatus) {
        // This is where you would integrate with alerting systems
        // Examples: email, Slack, PagerDuty, etc.
        
        logger.error('CRITICAL DATABASE ALERT', {
            message: 'Database has failed multiple consecutive health checks',
            health_status: healthStatus,
            consecutive_failures: this.consecutiveFailures,
            timestamp: new Date().toISOString(),
            action_required: 'Immediate investigation required'
        });

        // Example: Send email alert (implement based on your needs)
        // await this.sendEmailAlert(healthStatus);
        
        // Example: Send Slack notification (implement based on your needs)
        // await this.sendSlackAlert(healthStatus);
    }

    getMonitoringStatus() {
        return {
            is_monitoring: this.isMonitoring,
            consecutive_failures: this.consecutiveFailures,
            alert_thresholds: this.alertThresholds,
            last_check: new Date().toISOString()
        };
    }
}

// Create singleton instance
const databaseMonitor = new DatabaseMonitor();

module.exports = databaseMonitor;