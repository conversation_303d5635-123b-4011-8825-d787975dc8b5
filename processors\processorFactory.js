const CSVProcessor = require('./csvProcessor');
const PDFProcessor = require('./pdfProcessor');
const OFXProcessor = require('./ofxProcessor');
const logger = require('../utils/logger');

class ProcessorFactory {
    static getProcessor(fileType, mimeType) {
        logger.info(`Getting processor for file type: ${fileType}, mime: ${mimeType}`);
        
        // Normalize file type
        const normalizedType = fileType.toLowerCase();
        
        switch (normalizedType) {
            case 'csv':
            case 'text/csv':
            case 'application/csv':
                return new CSVProcessor();
                
            case 'pdf':
            case 'application/pdf':
                return new PDFProcessor();
                
            case 'ofx':
            case 'qfx':
            case 'application/x-ofx':
            case 'application/vnd.intu.qfx':
                return new OFXProcessor();
                
            default:
                // Try to determine by mime type
                if (mimeType) {
                    if (mimeType.includes('csv')) {
                        return new CSVProcessor();
                    }
                    if (mimeType.includes('pdf')) {
                        return new PDFProcessor();
                    }
                    if (mimeType.includes('ofx') || mimeType.includes('qfx')) {
                        return new OFXProcessor();
                    }
                }
                
                throw new Error(`Unsupported file type: ${fileType} (${mimeType})`);
        }
    }

    static getSupportedFormats() {
        return {
            csv: {
                extensions: ['.csv'],
                mimeTypes: ['text/csv', 'application/csv'],
                description: 'Comma Separated Values'
            },
            pdf: {
                extensions: ['.pdf'],
                mimeTypes: ['application/pdf'],
                description: 'Portable Document Format'
            },
            ofx: {
                extensions: ['.ofx', '.qfx'],
                mimeTypes: ['application/x-ofx', 'application/vnd.intu.qfx'],
                description: 'Open Financial Exchange'
            }
        };
    }

    static isSupported(fileType, mimeType) {
        try {
            this.getProcessor(fileType, mimeType);
            return true;
        } catch (error) {
            return false;
        }
    }
}

module.exports = ProcessorFactory;