const ExcelJS = require('exceljs');
const Papa = require('papaparse');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

class ExportService {
    // Main export method that handles both Excel and CSV
    async exportTransactions(userId, filters = {}, format = 'excel') {
        try {
            const transactions = await this.getFilteredTransactions(userId, filters);
            
            if (format === 'csv') {
                return this.exportTransactionsToCSV(transactions);
            } else {
                return this.exportTransactionsToExcel(userId, transactions, filters);
            }
        } catch (error) {
            logger.error(`Failed to export transactions (${format}):`, error);
            throw error;
        }
    }

    // CSV Export Implementation
    async exportTransactionsToCSV(transactions) {
        try {
            // Remove duplicates
            const uniqueTransactions = this.removeDuplicateTransactions(transactions);
            
            // Prepare data for CSV
            const csvData = uniqueTransactions.map(transaction => ({
                'Transaction ID': transaction.id,
                'Date': this.formatDate(transaction.transaction_date),
                'Description': transaction.description || '',
                'Category': transaction.category_name || 'Uncategorized',
                'Amount': parseFloat(transaction.amount || 0),
                'Type': transaction.type || '',
                'Account': transaction.account_name || '',
                'Merchant': transaction.merchant || '',
                'Location': transaction.location || '',
                'Tags': transaction.tags ? transaction.tags.join(', ') : ''
            }));

            // Remove empty columns
            const cleanedData = this.removeEmptyColumns(csvData);

            // Convert to CSV string
            const csv = Papa.unparse(cleanedData, {
                header: true,
                delimiter: ',',
                quotes: true
            });

            // Add export timestamp as comment
            const timestamp = new Date().toISOString();
            const csvWithTimestamp = `# Exported on ${timestamp}\n${csv}`;

            return Buffer.from(csvWithTimestamp, 'utf8');
        } catch (error) {
            logger.error('Failed to export transactions to CSV:', error);
            throw error;
        }
    }

    // Enhanced Excel Export Implementation
    async exportTransactionsToExcel(userId, transactions, filters = {}) {
        try {
            // Remove duplicates
            const uniqueTransactions = this.removeDuplicateTransactions(transactions);
            
            const workbook = new ExcelJS.Workbook();
            
            // Set workbook properties
            workbook.creator = 'Finance Manager';
            workbook.created = new Date();
            workbook.modified = new Date();
            
            // Main transactions sheet
            const worksheet = workbook.addWorksheet('Transactions');
            
            // Determine which columns to include based on data availability
            const columnConfig = this.determineColumnsToInclude(uniqueTransactions);
            
            // Define columns dynamically
            worksheet.columns = columnConfig.map(col => ({
                header: col.header,
                key: col.key,
                width: col.width
            }));

            // Style the header row
            worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFFFF' } };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FF366092' }
            };
            worksheet.getRow(1).alignment = { horizontal: 'center' };

            // Add data with conditional formatting
            uniqueTransactions.forEach((transaction, index) => {
                const rowData = {};
                
                columnConfig.forEach(col => {
                    switch (col.key) {
                        case 'transaction_id':
                            rowData[col.key] = transaction.id;
                            break;
                        case 'date':
                            rowData[col.key] = transaction.transaction_date;
                            break;
                        case 'description':
                            rowData[col.key] = transaction.description || '';
                            break;
                        case 'category':
                            rowData[col.key] = transaction.category_name || 'Uncategorized';
                            break;
                        case 'amount':
                            rowData[col.key] = parseFloat(transaction.amount || 0);
                            break;
                        case 'type':
                            rowData[col.key] = transaction.type || '';
                            break;
                        case 'account':
                            rowData[col.key] = transaction.account_name || '';
                            break;
                        case 'merchant':
                            rowData[col.key] = transaction.merchant || '';
                            break;
                        case 'location':
                            rowData[col.key] = transaction.location || '';
                            break;
                        case 'tags':
                            rowData[col.key] = transaction.tags ? transaction.tags.join(', ') : '';
                            break;
                    }
                });

                const row = worksheet.addRow(rowData);

                // Color code based on type
                if (transaction.type === 'expense') {
                    row.getCell('amount').font = { color: { argb: 'FFDC3545' } };
                    row.getCell('amount').numFmt = '-$#,##0.00';
                } else if (transaction.type === 'income') {
                    row.getCell('amount').font = { color: { argb: 'FF28A745' } };
                    row.getCell('amount').numFmt = '$#,##0.00';
                }

                // Format date
                row.getCell('date').numFmt = 'mm/dd/yyyy';
                
                // Alternate row colors
                if (index % 2 === 0) {
                    row.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFF8F9FA' }
                    };
                }
            });

            // Add borders to all cells
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });

            // Add summary sheet
            await this.addEnhancedSummarySheet(workbook, userId, filters, uniqueTransactions);

            // Add enhanced charts sheet
            await this.addEnhancedChartsSheet(workbook, userId, uniqueTransactions);

            return workbook.xlsx.writeBuffer();
        } catch (error) {
            logger.error('Failed to export transactions to Excel:', error);
            throw error;
        }
    }

    // Remove duplicate transactions
    removeDuplicateTransactions(transactions) {
        const seen = new Set();
        return transactions.filter(transaction => {
            // Create a unique key based on critical fields
            const key = `${transaction.transaction_date}_${transaction.description}_${transaction.category_name}_${transaction.amount}_${transaction.account_name}`;
            
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    // Determine which columns to include based on data availability
    determineColumnsToInclude(transactions) {
        const baseColumns = [
            { header: 'Transaction ID', key: 'transaction_id', width: 15 },
            { header: 'Date', key: 'date', width: 12 },
            { header: 'Description', key: 'description', width: 30 },
            { header: 'Category', key: 'category', width: 15 },
            { header: 'Amount', key: 'amount', width: 12 },
            { header: 'Type', key: 'type', width: 10 },
            { header: 'Account', key: 'account', width: 15 }
        ];

        const optionalColumns = [
            { header: 'Merchant', key: 'merchant', width: 20, field: 'merchant' },
            { header: 'Location', key: 'location', width: 20, field: 'location' },
            { header: 'Tags', key: 'tags', width: 25, field: 'tags' }
        ];

        // Check if optional columns have data
        const columnsToInclude = [...baseColumns];
        
        optionalColumns.forEach(col => {
            const hasData = transactions.some(t => {
                const value = t[col.field];
                return value && value !== '' && (Array.isArray(value) ? value.length > 0 : true);
            });
            
            if (hasData) {
                columnsToInclude.push(col);
            }
        });

        return columnsToInclude;
    }

    // Remove empty columns from CSV data
    removeEmptyColumns(data) {
        if (data.length === 0) return data;
        
        const columnsToRemove = [];
        const firstRow = data[0];
        
        // Check each column
        Object.keys(firstRow).forEach(column => {
            const hasData = data.some(row => {
                const value = row[column];
                return value && value !== '' && value !== 0;
            });
            
            if (!hasData) {
                columnsToRemove.push(column);
            }
        });

        // Remove empty columns
        return data.map(row => {
            const cleanRow = { ...row };
            columnsToRemove.forEach(col => delete cleanRow[col]);
            return cleanRow;
        });
    }

    // Enhanced summary sheet
    async addEnhancedSummarySheet(workbook, userId, filters, transactions) {
        const summarySheet = workbook.addWorksheet('Summary');
        
        // Get summary data
        const summaryData = await this.getEnhancedSummaryData(userId, filters, transactions);
        
        // Title
        summarySheet.mergeCells('A1:B1');
        const titleCell = summarySheet.getCell('A1');
        titleCell.value = 'Transaction Summary Report';
        titleCell.font = { bold: true, size: 16, color: { argb: 'FF366092' } };
        titleCell.alignment = { horizontal: 'center' };
        
        // Export timestamp
        summarySheet.mergeCells('A2:B2');
        const timestampCell = summarySheet.getCell('A2');
        timestampCell.value = `Exported on: ${new Date().toLocaleString()}`;
        timestampCell.font = { italic: true, size: 10 };
        timestampCell.alignment = { horizontal: 'center' };

        // Headers
        let row = 4;
        const headerRow = summarySheet.getRow(row);
        headerRow.getCell(1).value = 'Metric';
        headerRow.getCell(2).value = 'Value';
        headerRow.font = { bold: true };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE9ECEF' }
        };

        // Summary metrics
        const metrics = [
            { label: 'Total Transactions', value: summaryData.transactionCount },
            { label: 'Date Range', value: summaryData.dateRange },
            { label: 'Total Income', value: summaryData.totalIncome, format: '$#,##0.00', color: 'FF28A745' },
            { label: 'Total Expenses', value: summaryData.totalExpenses, format: '$#,##0.00', color: 'FFDC3545' },
            { label: 'Net Amount', value: summaryData.netAmount, format: '$#,##0.00', color: summaryData.netAmount >= 0 ? 'FF28A745' : 'FFDC3545' },
            { label: 'Average Transaction', value: summaryData.avgTransaction, format: '$#,##0.00' }
        ];

        metrics.forEach(metric => {
            if (metric.value !== null && metric.value !== undefined && !isNaN(metric.value)) {
                row++;
                const dataRow = summarySheet.getRow(row);
                dataRow.getCell(1).value = metric.label;
                dataRow.getCell(2).value = metric.value;
                
                if (metric.format) {
                    dataRow.getCell(2).numFmt = metric.format;
                }
                if (metric.color) {
                    dataRow.getCell(2).font = { color: { argb: metric.color } };
                }
            }
        });

        // Top categories section
        if (summaryData.topCategories.length > 0) {
            row += 2;
            summarySheet.getCell(`A${row}`).value = 'Top Spending Categories';
            summarySheet.getCell(`A${row}`).font = { bold: true };
            
            summaryData.topCategories.forEach(category => {
                row++;
                summarySheet.getCell(`A${row}`).value = category.name;
                summarySheet.getCell(`B${row}`).value = parseFloat(category.total);
                summarySheet.getCell(`B${row}`).numFmt = '$#,##0.00';
            });
        }

        // Auto-fit columns
        summarySheet.columns.forEach(column => {
            column.width = 25;
        });
    }

    // Enhanced charts data sheet
    async addEnhancedChartsSheet(workbook, userId, transactions) {
        const chartsSheet = workbook.addWorksheet('Charts Data');
        
        // Monthly data
        const monthlyData = this.getMonthlyChartData(transactions);
        
        // Headers
        chartsSheet.getRow(1).values = ['Month', 'Income', 'Expenses', 'Net Amount'];
        chartsSheet.getRow(1).font = { bold: true };
        
        // Data
        let row = 2;
        Object.entries(monthlyData).forEach(([month, data]) => {
            chartsSheet.getRow(row).values = [
                month,
                data.income,
                data.expenses,
                data.net
            ];
            row++;
        });

        // Format currency columns
        ['B', 'C', 'D'].forEach(col => {
            chartsSheet.getColumn(col).numFmt = '$#,##0.00';
        });

        // Category breakdown
        const categoryData = this.getCategoryChartData(transactions);
        
        // Add category data starting from column F
        chartsSheet.getCell('F1').value = 'Category';
        chartsSheet.getCell('G1').value = 'Total Amount';
        chartsSheet.getCell('H1').value = 'Transaction Count';
        
        row = 2;
        Object.entries(categoryData).forEach(([category, data]) => {
            chartsSheet.getCell(`F${row}`).value = category;
            chartsSheet.getCell(`G${row}`).value = data.total;
            chartsSheet.getCell(`H${row}`).value = data.count;
            row++;
        });

        // Format currency column
        chartsSheet.getColumn('G').numFmt = '$#,##0.00';
    }

    // Get enhanced summary data
    async getEnhancedSummaryData(userId, filters, transactions) {
        const summary = {
            totalIncome: 0,
            totalExpenses: 0,
            netAmount: 0,
            transactionCount: transactions.length,
            avgTransaction: 0,
            topCategories: [],
            dateRange: 'All Time'
        };

        const categoryTotals = {};

        transactions.forEach(transaction => {
            const amount = parseFloat(transaction.amount || 0);
            
            if (transaction.type === 'income') {
                summary.totalIncome += amount;
            } else if (transaction.type === 'expense') {
                summary.totalExpenses += Math.abs(amount);
                
                // Track category spending
                const categoryName = transaction.category_name || 'Uncategorized';
                categoryTotals[categoryName] = (categoryTotals[categoryName] || 0) + Math.abs(amount);
            }
        });

        summary.netAmount = summary.totalIncome - summary.totalExpenses;
        summary.avgTransaction = summary.transactionCount > 0 ? 
            (summary.totalIncome + summary.totalExpenses) / summary.transactionCount : 0;
        
        // Get top 5 categories
        summary.topCategories = Object.entries(categoryTotals)
            .map(([name, total]) => ({ name, total }))
            .sort((a, b) => b.total - a.total)
            .slice(0, 5);

        // Determine date range
        if (filters.start_date && filters.end_date) {
            summary.dateRange = `${filters.start_date} to ${filters.end_date}`;
        } else if (transactions.length > 0) {
            const dates = transactions.map(t => new Date(t.transaction_date)).sort();
            const startDate = dates[0].toISOString().split('T')[0];
            const endDate = dates[dates.length - 1].toISOString().split('T')[0];
            summary.dateRange = `${startDate} to ${endDate}`;
        }

        return summary;
    }

    // Get monthly chart data
    getMonthlyChartData(transactions) {
        const monthlyData = {};
        
        transactions.forEach(transaction => {
            const month = new Date(transaction.transaction_date).toISOString().slice(0, 7); // YYYY-MM
            const amount = parseFloat(transaction.amount || 0);
            
            if (!monthlyData[month]) {
                monthlyData[month] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'income') {
                monthlyData[month].income += amount;
            } else if (transaction.type === 'expense') {
                monthlyData[month].expenses += Math.abs(amount);
            }
        });

        // Calculate net amounts
        Object.keys(monthlyData).forEach(month => {
            monthlyData[month].net = monthlyData[month].income - monthlyData[month].expenses;
        });

        return monthlyData;
    }

    // Get category chart data
    getCategoryChartData(transactions) {
        const categoryData = {};
        
        transactions.forEach(transaction => {
            if (transaction.type === 'expense') {
                const category = transaction.category_name || 'Uncategorized';
                const amount = Math.abs(parseFloat(transaction.amount || 0));
                
                if (!categoryData[category]) {
                    categoryData[category] = { total: 0, count: 0 };
                }
                
                categoryData[category].total += amount;
                categoryData[category].count += 1;
            }
        });

        return categoryData;
    }

    // Get filtered transactions with enhanced query
    async getFilteredTransactions(userId, filters) {
        let query = `
            SELECT 
                t.*,
                c.name as category_name,
                a.name as account_name
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN accounts a ON t.account_id = a.id
            WHERE t.user_id = $1
        `;
        
        const params = [userId];
        let paramIndex = 2;

        if (filters.start_date && filters.end_date) {
            query += ` AND t.transaction_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
            params.push(filters.start_date, filters.end_date);
            paramIndex += 2;
        }

        if (filters.category_id) {
            query += ` AND t.category_id = $${paramIndex}`;
            params.push(filters.category_id);
            paramIndex++;
        }

        if (filters.account_id) {
            query += ` AND t.account_id = $${paramIndex}`;
            params.push(filters.account_id);
            paramIndex++;
        }

        if (filters.type) {
            query += ` AND t.type = $${paramIndex}`;
            params.push(filters.type);
            paramIndex++;
        }

        if (filters.min_amount !== undefined) {
            query += ` AND ABS(t.amount) >= $${paramIndex}`;
            params.push(parseFloat(filters.min_amount));
            paramIndex++;
        }

        if (filters.max_amount !== undefined) {
            query += ` AND ABS(t.amount) <= $${paramIndex}`;
            params.push(parseFloat(filters.max_amount));
            paramIndex++;
        }

        query += ' ORDER BY t.transaction_date DESC, t.created_at DESC';

        const result = await pool.query(query, params);
        return result.rows;
    }

    // Format date helper
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US');
    }
}

module.exports = new ExportService();