const axios = require('axios');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

class CurrencyService {
    constructor() {
        this.apiKey = process.env.EXCHANGE_RATE_API_KEY;
        this.baseUrl = 'https://api.exchangerate-api.com/v4/latest';
        this.cache = getRedisClient();
    }

    async getExchangeRates(baseCurrency = 'USD') {
        const cacheKey = `exchange_rates:${baseCurrency}`;
        
        try {
            // Try to get from cache first
            if (this.cache) {
                const cached = await this.cache.get(cacheKey);
                if (cached) {
                    return JSON.parse(cached);
                }
            }

            // Fetch from API
            const response = await axios.get(`${this.baseUrl}/${baseCurrency}`);
            const rates = response.data;

            // Cache for 1 hour
            if (this.cache) {
                await this.cache.setEx(cacheKey, 3600, JSON.stringify(rates));
            }

            return rates;
        } catch (error) {
            logger.error('Failed to fetch exchange rates:', error);
            throw new Error('Currency conversion service unavailable');
        }
    }

    async convertCurrency(amount, fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) {
            return amount;
        }

        try {
            const rates = await this.getExchangeRates(fromCurrency);
            const rate = rates.rates[toCurrency];
            
            if (!rate) {
                throw new Error(`Exchange rate not found for ${toCurrency}`);
            }

            return amount * rate;
        } catch (error) {
            logger.error('Currency conversion failed:', error);
            throw error;
        }
    }

    async getSupportedCurrencies() {
        const cacheKey = 'supported_currencies';
        
        try {
            if (this.cache) {
                const cached = await this.cache.get(cacheKey);
                if (cached) {
                    return JSON.parse(cached);
                }
            }

            const response = await axios.get('https://api.exchangerate-api.com/v4/symbols');
            const currencies = response.data;

            if (this.cache) {
                await this.cache.setEx(cacheKey, 86400, JSON.stringify(currencies)); // Cache for 24 hours
            }

            return currencies;
        } catch (error) {
            logger.error('Failed to fetch supported currencies:', error);
            // Return common currencies as fallback
            return {
                USD: 'US Dollar',
                EUR: 'Euro',
                GBP: 'British Pound',
                JPY: 'Japanese Yen',
                CAD: 'Canadian Dollar',
                AUD: 'Australian Dollar'
            };
        }
    }
}

module.exports = new CurrencyService();