const axios = require('axios');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

class CurrencyService {
    constructor() {
        this.apiKey = process.env.EXCHANGE_RATE_API_KEY;
        this.baseUrl = 'https://api.exchangerate-api.com/v4/latest';
        this.cache = getRedisClient();
    }

    async getExchangeRates(baseCurrency = 'USD') {
        const cacheKey = `exchange_rates:${baseCurrency}`;
        
        try {
            // Try to get from cache first
            if (this.cache) {
                const cached = await this.cache.get(cacheKey);
                if (cached) {
                    return JSON.parse(cached);
                }
            }

            // Fetch from API
            const response = await axios.get(`${this.baseUrl}/${baseCurrency}`);
            const rates = response.data;

            // Cache for 1 hour
            if (this.cache) {
                await this.cache.setEx(cacheKey, 3600, JSON.stringify(rates));
            }

            return rates;
        } catch (error) {
            logger.error('Failed to fetch exchange rates:', error);
            throw new Error('Currency conversion service unavailable');
        }
    }

    async convertCurrency(amount, fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) {
            return amount;
        }

        try {
            const rates = await this.getExchangeRates(fromCurrency);
            const rate = rates.rates[toCurrency];
            
            if (!rate) {
                throw new Error(`Exchange rate not found for ${toCurrency}`);
            }

            return amount * rate;
        } catch (error) {
            logger.error('Currency conversion failed:', error);
            throw error;
        }
    }

    async getSupportedCurrencies() {
        const cacheKey = 'supported_currencies';

        try {
            if (this.cache) {
                const cached = await this.cache.get(cacheKey);
                if (cached) {
                    return JSON.parse(cached);
                }
            }

            // Try to fetch from API first
            let currencyData = {};
            try {
                const response = await axios.get('https://api.exchangerate-api.com/v4/symbols');
                currencyData = response.data;
            } catch (apiError) {
                logger.warn('External currency API failed, using fallback data');
                currencyData = this.getFallbackCurrencies();
            }

            // Transform to frontend-friendly format
            const currencies = this.transformCurrencyData(currencyData);

            if (this.cache) {
                await this.cache.setEx(cacheKey, 86400, JSON.stringify(currencies)); // Cache for 24 hours
            }

            return currencies;
        } catch (error) {
            logger.error('Failed to fetch supported currencies:', error);
            // Return transformed fallback currencies
            return this.transformCurrencyData(this.getFallbackCurrencies());
        }
    }

    getFallbackCurrencies() {
        return {
            USD: 'US Dollar',
            EUR: 'Euro',
            GBP: 'British Pound',
            JPY: 'Japanese Yen',
            CAD: 'Canadian Dollar',
            AUD: 'Australian Dollar',
            CHF: 'Swiss Franc',
            CNY: 'Chinese Yuan',
            INR: 'Indian Rupee',
            KRW: 'South Korean Won',
            SGD: 'Singapore Dollar',
            HKD: 'Hong Kong Dollar',
            NOK: 'Norwegian Krone',
            SEK: 'Swedish Krona',
            DKK: 'Danish Krone',
            PLN: 'Polish Zloty',
            CZK: 'Czech Koruna',
            HUF: 'Hungarian Forint',
            RUB: 'Russian Ruble',
            BRL: 'Brazilian Real',
            MXN: 'Mexican Peso',
            ZAR: 'South African Rand',
            TRY: 'Turkish Lira',
            NZD: 'New Zealand Dollar'
        };
    }

    transformCurrencyData(currencyData) {
        const currencySymbols = {
            USD: '$', EUR: '€', GBP: '£', JPY: '¥', CAD: 'C$', AUD: 'A$',
            CHF: 'CHF', CNY: '¥', INR: '₹', KRW: '₩', SGD: 'S$', HKD: 'HK$',
            NOK: 'kr', SEK: 'kr', DKK: 'kr', PLN: 'zł', CZK: 'Kč', HUF: 'Ft',
            RUB: '₽', BRL: 'R$', MXN: '$', ZAR: 'R', TRY: '₺', NZD: 'NZ$'
        };

        return Object.entries(currencyData).map(([code, name]) => ({
            code: code,
            name: name,
            symbol: currencySymbols[code] || code
        }));
    }
}

module.exports = new CurrencyService();