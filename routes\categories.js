const express = require('express');
const { body } = require('express-validator');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Validation rules for categories
const validateCategory = [
    body('name')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Category name must be between 1 and 255 characters'),
    body('color')
        .optional()
        .matches(/^#[0-9A-F]{6}$/i)
        .withMessage('Color must be a valid hex color code'),
    body('icon')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Icon name must be 50 characters or less'),
    body('parent_id')
        .optional()
        .isUUID()
        .withMessage('Parent ID must be a valid UUID'),
    handleValidationErrors
];

// Get all categories for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { include_hierarchy } = req.query;

        let query = `
            SELECT 
                c.*,
                (SELECT COUNT(*) FROM transactions WHERE category_id = c.id) as transaction_count
            FROM categories c
            WHERE c.user_id = $1 AND c.is_active = true
            ORDER BY c.is_default DESC, c.name ASC
        `;

        const result = await pool.query(query, [userId]);
        let categories = result.rows.map(cat => ({
            ...cat,
            transaction_count: parseInt(cat.transaction_count)
        }));

        if (include_hierarchy === 'true') {
            categories = buildCategoryHierarchy(categories);
        }

        res.json({ categories });
    } catch (error) {
        next(error);
    }
});

// Get category hierarchy
router.get('/hierarchy', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await pool.query(`
            SELECT 
                c.*,
                (SELECT COUNT(*) FROM transactions WHERE category_id = c.id) as transaction_count
            FROM categories c
            WHERE c.user_id = $1 AND c.is_active = true
            ORDER BY c.name ASC
        `, [userId]);

        const categories = result.rows.map(cat => ({
            ...cat,
            transaction_count: parseInt(cat.transaction_count)
        }));

        const hierarchy = buildCategoryHierarchy(categories);

        res.json({ 
            hierarchy,
            total_categories: categories.length
        });
    } catch (error) {
        next(error);
    }
});

// Create category (Premium: unlimited, Free: limited)
router.post('/', authenticateToken, validateCategory, async (req, res, next) => {
    try {
        const userId = req.userId;
        const user = req.user;
        const { name, color, icon, parent_id } = req.body;

        // Check category limits for free users
        if (user.subscription_tier === 'free') {
            const countResult = await pool.query(
                'SELECT COUNT(*) FROM categories WHERE user_id = $1 AND is_default = false',
                [userId]
            );
            const customCategoryCount = parseInt(countResult.rows[0].count);
            
            if (customCategoryCount >= 5) {
                return res.status(403).json({
                    error: 'Free users are limited to 5 custom categories',
                    upgrade_url: '/api/user/upgrade-premium'
                });
            }
        }

        // Verify parent category exists and belongs to user if specified
        if (parent_id) {
            const parentCheck = await pool.query(
                'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
                [parent_id, userId]
            );
            
            if (parentCheck.rows.length === 0) {
                return res.status(404).json({ error: 'Parent category not found' });
            }
        }

        // Create category
        const result = await pool.query(`
            INSERT INTO categories (user_id, name, color, icon, parent_id)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `, [userId, name, color, icon, parent_id]);

        const category = result.rows[0];

        logger.info(`Category created: ${category.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Category created successfully',
            category
        });
    } catch (error) {
        next(error);
    }
});

// Update category
router.put('/:id', authenticateToken, validateCategory, async (req, res, next) => {
    try {
        const userId = req.userId;
        const categoryId = req.params.id;
        const { name, color, icon, parent_id, is_active } = req.body;

        // Check if category exists and belongs to user
        const categoryCheck = await pool.query(
            'SELECT * FROM categories WHERE id = $1 AND user_id = $2',
            [categoryId, userId]
        );

        if (categoryCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Category not found' });
        }

        const existingCategory = categoryCheck.rows[0];

        // Prevent modification of default categories for some fields
        if (existingCategory.is_default && (name !== existingCategory.name)) {
            return res.status(400).json({ 
                error: 'Cannot rename default categories' 
            });
        }

        // Verify parent category if specified
        if (parent_id && parent_id !== categoryId) {
            const parentCheck = await pool.query(
                'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
                [parent_id, userId]
            );
            
            if (parentCheck.rows.length === 0) {
                return res.status(404).json({ error: 'Parent category not found' });
            }

            // Prevent circular references
            if (await hasCircularReference(categoryId, parent_id)) {
                return res.status(400).json({ 
                    error: 'Cannot set parent - would create circular reference' 
                });
            }
        }

        // Update category
        const result = await pool.query(`
            UPDATE categories 
            SET name = $3, color = $4, icon = $5, parent_id = $6, is_active = COALESCE($7, is_active)
            WHERE id = $1 AND user_id = $2
            RETURNING *
        `, [categoryId, userId, name, color, icon, parent_id, is_active]);

        const category = result.rows[0];

        logger.info(`Category updated: ${categoryId} for user: ${userId}`);

        res.json({
            message: 'Category updated successfully',
            category
        });
    } catch (error) {
        next(error);
    }
});

// Soft delete category
router.delete('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const categoryId = req.params.id;

        // Check if category exists and belongs to user
        const categoryCheck = await pool.query(
            'SELECT * FROM categories WHERE id = $1 AND user_id = $2',
            [categoryId, userId]
        );

        if (categoryCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Category not found' });
        }

        const category = categoryCheck.rows[0];

        // Prevent deletion of default categories
        if (category.is_default) {
            return res.status(400).json({ 
                error: 'Cannot delete default categories' 
            });
        }

        // Check if category has transactions
        const transactionCheck = await pool.query(
            'SELECT COUNT(*) FROM transactions WHERE category_id = $1',
            [categoryId]
        );

        const transactionCount = parseInt(transactionCheck.rows[0].count);

        if (transactionCount > 0) {
            // Soft delete (deactivate) if has transactions
            await pool.query(
                'UPDATE categories SET is_active = false WHERE id = $1 AND user_id = $2',
                [categoryId, userId]
            );

            logger.info(`Category deactivated: ${categoryId} for user: ${userId}`);

            res.json({ 
                message: 'Category deactivated successfully (has existing transactions)',
                deactivated: true
            });
        } else {
            // Hard delete if no transactions
            await pool.query(
                'DELETE FROM categories WHERE id = $1 AND user_id = $2',
                [categoryId, userId]
            );

            logger.info(`Category deleted: ${categoryId} for user: ${userId}`);

            res.json({ 
                message: 'Category deleted successfully',
                deleted: true
            });
        }
    } catch (error) {
        next(error);
    }
});

// Helper function to build category hierarchy
function buildCategoryHierarchy(categories) {
    const categoryMap = new Map();
    const rootCategories = [];

    // Create a map of all categories
    categories.forEach(category => {
        categoryMap.set(category.id, { ...category, children: [] });
    });

    // Build the hierarchy
    categories.forEach(category => {
        if (category.parent_id && categoryMap.has(category.parent_id)) {
            const parent = categoryMap.get(category.parent_id);
            parent.children.push(categoryMap.get(category.id));
        } else {
            rootCategories.push(categoryMap.get(category.id));
        }
    });

    return rootCategories;
}

// Helper function to check for circular references
async function hasCircularReference(categoryId, parentId) {
    const visited = new Set();
    let currentId = parentId;

    while (currentId && !visited.has(currentId)) {
        if (currentId === categoryId) {
            return true; // Circular reference found
        }

        visited.add(currentId);

        const result = await pool.query(
            'SELECT parent_id FROM categories WHERE id = $1',
            [currentId]
        );

        if (result.rows.length === 0) {
            break;
        }

        currentId = result.rows[0].parent_id;
    }

    return false;
}

module.exports = router;