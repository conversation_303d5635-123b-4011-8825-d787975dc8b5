const express = require('express');
const { checkDatabaseHealth } = require('../config/database');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

const router = express.Router();

// Basic health check
router.get('/', (req, res) => {
    res.status(200).json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
    });
});

// Database health check
router.get('/database', async (req, res) => {
    try {
        const healthCheck = await checkDatabaseHealth();
        
        if (healthCheck.status === 'healthy') {
            res.status(200).json(healthCheck);
        } else {
            res.status(503).json(healthCheck);
        }
    } catch (error) {
        logger.error('Database health check endpoint failed:', error);
        res.status(503).json({ 
            status: 'error',
            error: 'Health check failed',
            timestamp: new Date().toISOString()
        });
    }
});

// Redis health check
router.get('/redis', async (req, res) => {
    try {
        const redisClient = getRedisClient();
        
        if (!redisClient) {
            return res.status(503).json({
                status: 'unavailable',
                message: 'Redis client not initialized'
            });
        }

        const startTime = Date.now();
        await redisClient.ping();
        const latency = Date.now() - startTime;

        res.status(200).json({
            status: 'healthy',
            latency: latency,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Redis health check failed:', error);
        res.status(503).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Comprehensive system health check
router.get('/system', async (req, res) => {
    try {
        const [databaseHealth, redisHealth] = await Promise.allSettled([
            checkDatabaseHealth(),
            checkRedisHealth()
        ]);

        const systemHealth = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            services: {
                database: databaseHealth.status === 'fulfilled' ? databaseHealth.value : {
                    status: 'error',
                    error: databaseHealth.reason?.message || 'Unknown error'
                },
                redis: redisHealth.status === 'fulfilled' ? redisHealth.value : {
                    status: 'error',
                    error: redisHealth.reason?.message || 'Unknown error'
                }
            }
        };

        // Determine overall system status
        const hasUnhealthyService = Object.values(systemHealth.services).some(
            service => service.status === 'unhealthy' || service.status === 'error'
        );

        if (hasUnhealthyService) {
            systemHealth.status = 'degraded';
        }

        const statusCode = systemHealth.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(systemHealth);

    } catch (error) {
        logger.error('System health check failed:', error);
        res.status(503).json({
            status: 'error',
            error: 'System health check failed',
            timestamp: new Date().toISOString()
        });
    }
});

// Detailed database metrics (for monitoring)
router.get('/database/metrics', async (req, res) => {
    try {
        const healthCheck = await checkDatabaseHealth();
        
        // Additional metrics
        const metrics = {
            ...healthCheck,
            process_info: {
                pid: process.pid,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                cpu_usage: process.cpuUsage()
            },
            environment: {
                node_version: process.version,
                platform: process.platform,
                arch: process.arch
            }
        };

        res.status(200).json(metrics);
    } catch (error) {
        logger.error('Database metrics endpoint failed:', error);
        res.status(503).json({
            status: 'error',
            error: 'Metrics collection failed',
            timestamp: new Date().toISOString()
        });
    }
});

// Helper function for Redis health check
async function checkRedisHealth() {
    try {
        const redisClient = getRedisClient();
        
        if (!redisClient) {
            return {
                status: 'unavailable',
                message: 'Redis client not initialized'
            };
        }

        const startTime = Date.now();
        await redisClient.ping();
        const latency = Date.now() - startTime;

        return {
            status: 'healthy',
            latency: latency,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

module.exports = router;