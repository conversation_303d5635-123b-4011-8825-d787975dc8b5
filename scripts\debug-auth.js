const { pool } = require('../config/database');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

async function debugAuthentication() {
    try {
        console.log('🔍 Authentication Debug Report');
        console.log('================================');

        // 1. Check environment variables
        console.log('\n1. Environment Configuration:');
        console.log(`   JWT_SECRET configured: ${!!process.env.JWT_SECRET}`);
        console.log(`   JWT_REFRESH_SECRET configured: ${!!process.env.JWT_REFRESH_SECRET}`);
        console.log(`   JWT_SECRET length: ${process.env.JWT_SECRET ? process.env.JWT_SECRET.length : 0}`);
        console.log(`   JWT_REFRESH_SECRET length: ${process.env.JWT_REFRESH_SECRET ? process.env.JWT_REFRESH_SECRET.length : 0}`);

        // 2. Check database connection
        console.log('\n2. Database Connection:');
        try {
            const dbTest = await pool.query('SELECT NOW() as current_time');
            console.log(`   ✅ Database connected: ${dbTest.rows[0].current_time}`);
        } catch (dbError) {
            console.log(`   ❌ Database error: ${dbError.message}`);
            return;
        }

        // 3. Check test user
        console.log('\n3. Test User Verification:');
        const userResult = await pool.query(
            'SELECT id, email, password_hash, subscription_tier, refresh_token FROM users WHERE email = $1',
            ['<EMAIL>']
        );

        if (userResult.rows.length === 0) {
            console.log('   ❌ Test user not found. Running seed script...');
            // Run seed to create test user
            const hashedPassword = await bcrypt.hash('testpassword123', 12);
            const createResult = await pool.query(`
                INSERT INTO users (email, password_hash, subscription_tier)
                VALUES ('<EMAIL>', $1, 'premium')
                RETURNING id, email, subscription_tier
            `, [hashedPassword]);
            
            console.log(`   ✅ Test user created: ${createResult.rows[0].email}`);
        } else {
            const user = userResult.rows[0];
            console.log(`   ✅ Test user found: ${user.email}`);
            console.log(`   Subscription: ${user.subscription_tier}`);
            console.log(`   Has refresh token: ${!!user.refresh_token}`);
        }

        // 4. Test password verification
        console.log('\n4. Password Verification Test:');
        const testUser = await pool.query(
            'SELECT password_hash FROM users WHERE email = $1',
            ['<EMAIL>']
        );
        
        if (testUser.rows.length > 0) {
            const isPasswordValid = await bcrypt.compare('testpassword123', testUser.rows[0].password_hash);
            console.log(`   Password verification: ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`);
        }

        // 5. Test JWT token generation
        console.log('\n5. JWT Token Generation Test:');
        try {
            const testPayload = { userId: 'test-id', email: '<EMAIL>' };
            
            const accessToken = jwt.sign(
                testPayload,
                process.env.JWT_SECRET,
                { 
                    expiresIn: '15m',
                    issuer: 'finance-manager-api',
                    audience: 'finance-manager-app'
                }
            );

            const refreshToken = jwt.sign(
                { userId: 'test-id', type: 'refresh' },
                process.env.JWT_REFRESH_SECRET,
                { 
                    expiresIn: '7d',
                    issuer: 'finance-manager-api',
                    audience: 'finance-manager-app'
                }
            );

            console.log('   ✅ Access token generated successfully');
            console.log('   ✅ Refresh token generated successfully');

            // Test token verification
            const decoded = jwt.verify(accessToken, process.env.JWT_SECRET, {
                issuer: 'finance-manager-api',
                audience: 'finance-manager-app'
            });
            console.log('   ✅ Token verification successful');

        } catch (jwtError) {
            console.log(`   ❌ JWT error: ${jwtError.message}`);
        }

        // 6. Test complete login flow
        console.log('\n6. Complete Login Flow Test:');
        try {
            const loginUser = await pool.query(
                'SELECT id, email, password_hash, subscription_tier FROM users WHERE email = $1',
                ['<EMAIL>']
            );

            if (loginUser.rows.length > 0) {
                const user = loginUser.rows[0];
                
                // Generate tokens
                const accessToken = jwt.sign(
                    { 
                        userId: user.id, 
                        email: user.email,
                        subscription_tier: user.subscription_tier 
                    },
                    process.env.JWT_SECRET,
                    { 
                        expiresIn: '15m',
                        issuer: 'finance-manager-api',
                        audience: 'finance-manager-app'
                    }
                );

                const refreshToken = jwt.sign(
                    { 
                        userId: user.id,
                        type: 'refresh'
                    },
                    process.env.JWT_REFRESH_SECRET,
                    { 
                        expiresIn: '7d',
                        issuer: 'finance-manager-api',
                        audience: 'finance-manager-app'
                    }
                );

                // Store refresh token
                await pool.query(
                    'UPDATE users SET refresh_token = $1 WHERE id = $2',
                    [refreshToken, user.id]
                );

                console.log('   ✅ Complete login flow successful');
                console.log(`   Access token length: ${accessToken.length}`);
                console.log(`   Refresh token length: ${refreshToken.length}`);

                // Test response format
                const loginResponse = {
                    success: true,
                    message: 'Login successful',
                    user: {
                        id: user.id,
                        email: user.email,
                        subscription_tier: user.subscription_tier
                    },
                    tokens: {
                        access_token: accessToken,
                        refresh_token: refreshToken,
                        token_type: 'Bearer',
                        expires_in: 900
                    }
                };

                console.log('   ✅ Response format valid');
                console.log(`   Response has access_token: ${!!loginResponse.tokens.access_token}`);
                console.log(`   Response has refresh_token: ${!!loginResponse.tokens.refresh_token}`);

            }
        } catch (flowError) {
            console.log(`   ❌ Login flow error: ${flowError.message}`);
        }

        console.log('\n================================');
        console.log('🎉 Authentication debug completed');

    } catch (error) {
        console.error('Debug script failed:', error);
    } finally {
        process.exit(0);
    }
}

// Run debug if called directly
if (require.main === module) {
    debugAuthentication();
}

module.exports = debugAuthentication;