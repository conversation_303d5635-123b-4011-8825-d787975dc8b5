{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-24T13:24:56.824Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:09:21.404Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:48:51.693Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:50:39.277Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:51:57.162Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:53:54.071Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T16:14:56.587Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T13:25:18.335Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:43:18.099Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:44:12.012Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:45:53.125Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:47:08.850Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:47:21.649Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:47:51.877Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:48:16.776Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T17:48:21.667Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:12:29.568Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:13:54.896Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:14:43.264Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:15:22.911Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:16:00.146Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:17:18.379Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.cb7c0bca.js:31:29641","syscall":"connect","timestamp":"2025-06-26T18:18:01.694Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:78:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:23:36.587Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:78:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:23:48.622Z"}
{"database":"postgres","error":"Cannot read properties of undefined (reading 'processID')","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"TypeError: Cannot read properties of undefined (reading 'processID')\n    at BoundPool.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:44:27)\n    at BoundPool.emit (node:events:524:28)\n    at BoundPool.emit (node:domain:489:12)\n    at BoundPool._release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:353:10)\n    at Client.release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:342:12)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:82:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:25:18.436Z"}
{"database":"postgres","error":"Cannot read properties of undefined (reading 'processID')","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"TypeError: Cannot read properties of undefined (reading 'processID')\n    at BoundPool.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:44:27)\n    at BoundPool.emit (node:events:524:28)\n    at BoundPool.emit (node:domain:489:12)\n    at BoundPool._release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:353:10)\n    at Client.release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:342:12)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:82:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:25:20.997Z"}
{"database":"postgres","error":"Cannot read properties of undefined (reading 'processID')","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 3/5):","port":"5432","service":"finance-manager-api","stack":"TypeError: Cannot read properties of undefined (reading 'processID')\n    at BoundPool.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:44:27)\n    at BoundPool.emit (node:events:524:28)\n    at BoundPool.emit (node:domain:489:12)\n    at BoundPool._release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:353:10)\n    at Client.release (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:342:12)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:82:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:25:23.568Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.055Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.073Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.137Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.244Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.414Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.629Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:42.895Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:43.204Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:43.567Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:43.970Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-28T16:29:44.424Z"}
{"level":"error","message":"Refresh token error: Connection terminated unexpectedly","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:245:24","timestamp":"2025-06-28T16:45:52.623Z"}
{"body":{"refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIzMTg0OTFiZS1hNDJlLTQ0YjgtYWMyZS04NThlNjQxZWFlMzAiLCJ0eXBlIjoicmVmcmVzaCIsImlhdCI6MTc1MTExNzU2NiwiZXhwIjoxNzUxNzIyMzY2LCJhdWQiOiJmaW5hbmNlLW1hbmFnZXItYXBwIiwiaXNzIjoiZmluYW5jZS1tYW5hZ2VyLWFwaSJ9.ATaj9n2wqwuJNSMyzqSd1KNJ4CXRKSajWAsGzWiuEhw"},"level":"error","message":"Error: Connection terminated unexpectedly","method":"POST","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:245:24","timestamp":"2025-06-28T16:45:52.627Z","url":"/api/auth/refresh"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T16:46:00.251Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T16:46:00.786Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T16:46:00.791Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T16:46:00.881Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T16:46:48.251Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T16:46:48.254Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T16:46:48.434Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T16:46:48.742Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:51:41.120Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:51:53.135Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:52:09.872Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:52:21.893Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 3/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:52:33.914Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 4/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:52:45.940Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 5/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:87:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-28T16:52:57.962Z"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T16:56:46.800Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T16:56:47.743Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T16:56:47.748Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T16:56:47.752Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"level":"error","message":"Refresh token error: Connection terminated unexpectedly","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:245:24","timestamp":"2025-06-28T17:02:42.711Z"}
{"body":{"refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIzMTg0OTFiZS1hNDJlLTQ0YjgtYWMyZS04NThlNjQxZWFlMzAiLCJ0eXBlIjoicmVmcmVzaCIsImlhdCI6MTc1MTEyOTIwNywiZXhwIjoxNzUxNzM0MDA3LCJhdWQiOiJmaW5hbmNlLW1hbmFnZXItYXBwIiwiaXNzIjoiZmluYW5jZS1tYW5hZ2VyLWFwaSJ9.uTXbSzE1bXVvOM_ZbdKX7fjg-l2Y8inp0pYC5s20Yik"},"level":"error","message":"Error: Connection terminated unexpectedly","method":"POST","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:245:24","timestamp":"2025-06-28T17:02:42.713Z","url":"/api/auth/refresh"}
{"level":"error","message":"Login error: Connection terminated unexpectedly","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:121:24","timestamp":"2025-06-28T17:21:00.250Z"}
{"body":{"email":"<EMAIL>","password":"testpassword123"},"level":"error","message":"Error: Connection terminated unexpectedly","method":"POST","service":"finance-manager-api","stack":"Error: Connection terminated unexpectedly\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\auth.js:121:24","timestamp":"2025-06-28T17:21:00.253Z","url":"/api/auth/login"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-28T17:34:58.922Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-28T17:37:37.879Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-28T17:37:49.903Z"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:38:56.850Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:38:57.592Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:38:57.684Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:38:57.886Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:39:08.168Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:39:08.941Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:39:08.945Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:39:09.036Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:39:23.554Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:39:23.751Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:39:24.257Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:39:24.260Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-28T17:43:31.607Z","url":"/api/user/profile","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:43:33.386Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:43:33.942Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:43:33.948Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:43:34.139Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"error":"Connection terminated unexpectedly","idleConnections":1,"level":"error","message":"Database pool error:","processId":25088,"service":"finance-manager-api","timestamp":"2025-06-28T17:54:26.132Z","totalConnections":1,"waitingClients":0}
{"error":"Connection terminated unexpectedly","idleConnections":0,"level":"error","message":"Database pool error:","processId":25087,"service":"finance-manager-api","timestamp":"2025-06-28T17:54:26.133Z","totalConnections":0,"waitingClients":0}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-28T17:56:36.709Z","url":"/api/user/profile","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:56:38.124Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:56:38.611Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:56:38.615Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:56:38.686Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:57:43.160Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:57:43.337Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:57:43.559Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:57:43.562Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:58:15.902Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:58:16.041Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:58:16.576Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:58:16.585Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:58:47.513Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:58:47.666Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:58:48.202Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:58:48.204Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T17:58:52.357Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T17:58:52.796Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T17:58:52.798Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T17:58:52.855Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-28T17:59:58.860Z","url":"/api/user/profile","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T18:00:40.323Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T18:00:40.457Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T18:00:40.838Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T18:00:40.841Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"error":"Connection terminated unexpectedly","idleConnections":1,"level":"error","message":"Database pool error:","processId":25575,"service":"finance-manager-api","timestamp":"2025-06-28T18:28:52.445Z","totalConnections":1,"waitingClients":0}
{"error":"Connection terminated unexpectedly","idleConnections":0,"level":"error","message":"Database pool error:","processId":25842,"service":"finance-manager-api","timestamp":"2025-06-28T18:28:52.448Z","totalConnections":0,"waitingClients":0}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-28T18:38:26.838Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 1/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:39:28.554Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 2/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:39:30.572Z"}
{"error":"Cannot use a pool after calling end on the pool","level":"error","message":"Database connection failed (attempt 3/5):","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:39:34.589Z"}
{"error":"Cannot use a pool after calling end on the pool","level":"error","message":"Database connection failed (attempt 4/5):","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:39:42.598Z"}
{"level":"error","message":"Error during database shutdown: Called end on pool more than once","service":"finance-manager-api","stack":"Error: Called end on pool more than once\n    at BoundPool.end (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:453:19)\n    at process.gracefulShutdown (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:637:20)\n    at process.emit (node:events:536:35)","timestamp":"2025-06-28T18:39:44.517Z"}
{"error":"Cannot use a pool after calling end on the pool","level":"error","message":"Database connection failed (attempt 5/5):","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:39:58.611Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 1/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:40:15.544Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 2/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:40:17.564Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 3/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:40:21.579Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 4/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:40:29.589Z"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:5432","level":"error","message":"Database connection failed (attempt 5/5):","service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startWithMonitoring (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\start-with-monitoring.js:10:9)","timestamp":"2025-06-28T18:40:45.606Z"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T18:42:13.426Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T18:42:14.209Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T18:42:14.215Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T18:42:14.218Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-28T19:09:14.866Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-28T19:09:15.068Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-28T19:09:15.899Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-28T19:09:15.904Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T05:05:51.111Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T05:06:03.138Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 3/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T05:06:17.161Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 4/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T05:06:35.171Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 5/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T05:07:01.191Z"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:12:43.393Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:12:44.139Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:12:44.199Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:12:44.244Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:13:57.514Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:13:57.517Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:13:57.762Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:13:58.257Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:16:14.796Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:16:15.496Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:16:15.527Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:16:15.700Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:27:21.080Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:27:21.112Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:27:21.145Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:27:22.135Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:27:26.109Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:27:26.313Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:27:26.904Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:27:26.950Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-29T05:29:04.257Z","url":"/api/user/profile","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T05:29:18.924Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T05:29:19.640Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T05:29:19.644Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T05:29:19.872Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:118:9)","timestamp":"2025-06-29T14:34:25.119Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T14:34:37.148Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 3/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T14:34:51.157Z"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-29T14:45:33.200Z","url":"/api/user/profile","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T14:45:47.349Z","url":"/api/insights/dashboard","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T14:45:47.941Z","url":"/api/goals","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T14:45:47.952Z","url":"/api/recommendations","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T14:45:48.095Z","url":"/api/notifications/unread","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Migration failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigrations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:242:20)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:7:9)","syscall":"connect","timestamp":"2025-06-29T14:48:17.218Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Migration failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigrations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:242:20)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:7:9)","syscall":"connect","timestamp":"2025-06-29T14:48:59.151Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Migration failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigrations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:242:20)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:7:9)","syscall":"connect","timestamp":"2025-06-29T14:50:45.239Z"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-29T14:52:41.821Z","url":"/api/user/profile","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:192:24","timestamp":"2025-06-29T14:52:42.463Z","url":"/api/user/subscription-status","userId":"0a5f3725-72c1-4a9e-bbbb-ef3402b9f26e"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T14:53:22.217Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T14:53:22.741Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T14:53:22.756Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T14:53:22.761Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-29T14:57:45.865Z","url":"/api/user/profile","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T14:57:53.727Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T14:57:54.344Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T14:57:54.350Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T14:57:54.560Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\user.js:15:24","timestamp":"2025-06-29T15:10:21.454Z","url":"/api/user/profile","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\insights.js:25:32","timestamp":"2025-06-29T15:10:35.494Z","url":"/api/insights/dashboard","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"goals\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"goals\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\goals.js:69:24","timestamp":"2025-06-29T15:10:36.169Z","url":"/api/goals","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"ai_recommendations\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"ai_recommendations\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\recommendations.js:46:24","timestamp":"2025-06-29T15:10:36.198Z","url":"/api/recommendations","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"body":{},"level":"error","message":"Error: relation \"notifications\" does not exist","method":"GET","service":"finance-manager-api","stack":"error: relation \"notifications\" does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\routes\\notifications.js:108:24","timestamp":"2025-06-29T15:10:36.369Z","url":"/api/notifications/unread","userId":"318491be-a42e-44b8-ac2e-858e641eae30"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Migration failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async runMigrations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:242:20)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:7:9)","syscall":"connect","timestamp":"2025-06-29T15:49:58.547Z"}
{"database":"postgres","error":"Connection terminated due to connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 1/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:113:28)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:10:9)","timestamp":"2025-06-29T16:56:58.946Z"}
{"database":"postgres","error":"Connection timeout","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 2/5):","port":"5432","service":"finance-manager-api","stack":"Error: Connection timeout\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:116:45)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-06-29T16:57:10.973Z"}
{"database":"postgres","error":"Cannot use a pool after calling end on the pool","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 3/5):","port":"5432","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:10:9)","timestamp":"2025-06-29T16:57:14.981Z"}
{"database":"postgres","error":"Cannot use a pool after calling end on the pool","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 4/5):","port":"5432","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:10:9)","timestamp":"2025-06-29T16:57:22.996Z"}
{"database":"postgres","error":"Cannot use a pool after calling end on the pool","host":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Database connection failed (attempt 5/5):","port":"5432","service":"finance-manager-api","stack":"Error: Cannot use a pool after calling end on the pool\n    at BoundPool.connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:184:19)\n    at connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:114:22)\n    at async migrate (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\scripts\\migrate.js:10:9)","timestamp":"2025-06-29T16:57:39.010Z"}
{"code":"ECONNRESET","error":"read ECONNRESET","idleConnections":0,"level":"error","message":"Database pool error:","processId":26333,"service":"finance-manager-api","timestamp":"2025-06-29T17:34:16.045Z","totalConnections":0,"waitingClients":0}
{"code":"ENOTFOUND","errno":-3008,"hostname":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Recommendation cleanup failed: getaddrinfo ENOTFOUND finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","service":"finance-manager-api","stack":"Error: getaddrinfo ENOTFOUND finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\workers\\recommendationGenerator.js:26:13)","syscall":"getaddrinfo","timestamp":"2025-06-29T21:06:07.533Z"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","level":"error","message":"Daily recommendation generation failed: getaddrinfo ENOTFOUND finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com","service":"finance-manager-api","stack":"Error: getaddrinfo ENOTFOUND finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.generateDailyRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\workers\\recommendationGenerator.js:64:33)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\workers\\recommendationGenerator.js:16:13)","syscall":"getaddrinfo","timestamp":"2025-06-30T07:17:13.854Z"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T11:44:44.292Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T12:06:40.028Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T12:08:16.145Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T13:36:02.866Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T13:39:03.042Z","url":"/api/insights/dashboard","userId":"9db9b829-4146-4044-b272-2a53958de920"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T13:41:10.272Z","url":"/api/insights/dashboard","userId":"9db9b829-4146-4044-b272-2a53958de920"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T14:00:49.891Z","url":"/api/insights/dashboard","userId":"9db9b829-4146-4044-b272-2a53958de920"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T15:04:52.587Z","url":"/api/insights/dashboard","userId":"9db9b829-4146-4044-b272-2a53958de920"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T15:04:54.552Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T15:32:02.961Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T15:44:17.778Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T15:56:54.676Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T16:12:31.882Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T16:13:53.066Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: function pg_catalog.extract(unknown, integer) does not exist","method":"GET","service":"finance-manager-api","stack":"error: function pg_catalog.extract(unknown, integer) does not exist\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\insights.js:73:37","timestamp":"2025-07-06T18:18:01.092Z","url":"/api/insights/dashboard","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-06T21:07:58.277Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-08T07:11:46.448Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-09T14:20:25.921Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-10T05:31:07.877Z"}
{"body":{"id":"9bf74b9c-72f0-446d-bdd8-86e33f94c34a","message":"invalid api token"},"level":"error","message":"Failed to extract goal details: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"9bf74b9c-72f0-446d-bdd8-86e33f94c34a\",\n  \"message\": \"invalid api token\"\n}","service":"finance-manager-api","stack":"Error: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"9bf74b9c-72f0-446d-bdd8-86e33f94c34a\",\n  \"message\": \"invalid api token\"\n}\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:210:31)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":401,"timestamp":"2025-07-10T13:28:53.011Z"}
{"body":{"id":"e9d247e0-b71f-47df-b7a6-4bf9ce5552a3","message":"invalid api token"},"level":"error","message":"Failed to verify financial data: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"e9d247e0-b71f-47df-b7a6-4bf9ce5552a3\",\n  \"message\": \"invalid api token\"\n}","service":"finance-manager-api","stack":"Error: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"e9d247e0-b71f-47df-b7a6-4bf9ce5552a3\",\n  \"message\": \"invalid api token\"\n}\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:210:31)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":401,"timestamp":"2025-07-10T13:30:02.241Z"}
{"body":{"id":"fa2d20cb-5cc3-4fc9-af52-9c2d54893066","message":"invalid api token"},"level":"error","message":"Failed to extract goal details: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"fa2d20cb-5cc3-4fc9-af52-9c2d54893066\",\n  \"message\": \"invalid api token\"\n}","service":"finance-manager-api","stack":"Error: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"fa2d20cb-5cc3-4fc9-af52-9c2d54893066\",\n  \"message\": \"invalid api token\"\n}\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:210:31)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":401,"timestamp":"2025-07-10T13:30:39.050Z"}
{"body":{"id":"6268cc2b-b4d1-4e96-8a9c-419926f8e795","message":"invalid api token"},"level":"error","message":"Failed to verify financial data: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"6268cc2b-b4d1-4e96-8a9c-419926f8e795\",\n  \"message\": \"invalid api token\"\n}","service":"finance-manager-api","stack":"Error: UnauthorizedError\nStatus code: 401\nBody: {\n  \"id\": \"6268cc2b-b4d1-4e96-8a9c-419926f8e795\",\n  \"message\": \"invalid api token\"\n}\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:210:31)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","statusCode":401,"timestamp":"2025-07-10T13:30:56.019Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T13:35:12.503Z"}
{"level":"error","message":"Failed to extract goal details: fetch failed","service":"finance-manager-api","stack":"Error: fetch failed\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:247:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-10T13:44:34.616Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T13:44:42.389Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T13:47:35.582Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T18:35:10.140Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T18:36:10.632Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T18:45:36.445Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:01:06.588Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:21:05.054Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:28:13.650Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:28:35.608Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:30:46.159Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:30:46.487Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:30:46.625Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:30:46.656Z"}
{"level":"error","message":"Failed to extract goal details: Unexpected token '`', \"```json\n{\n\"... is not valid JSON","service":"finance-manager-api","stack":"SyntaxError: Unexpected token '`', \"```json\n{\n\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GoalAI.extractGoalDetails (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:436:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GoalAI.processGoalUnderstanding (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:157:36)\n    at async GoalAI.startGoalSession (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\goalAI.js:87:30)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:570:24","timestamp":"2025-07-10T19:30:46.981Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-10T20:33:43.719Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-07-12T20:30:00.276Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-08-10T04:40:20.189Z"}
{"level":"error","message":"Failed to generate AI recommendations: fetch failed","service":"finance-manager-api","stack":"Error: fetch failed\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:247:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-10T04:40:24.320Z"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-08-15T02:50:07.123Z"}
{"level":"error","message":"Failed to generate AI recommendations: fetch failed","service":"finance-manager-api","stack":"Error: fetch failed\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:247:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-15T02:50:10.070Z"}
{"level":"error","message":"Failed to generate recommendations: topSpending.total_amount.toFixed is not a function","service":"finance-manager-api","stack":"TypeError: topSpending.total_amount.toFixed is not a function\n    at RecommendationEngine.generateFallbackRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:492:69)\n    at RecommendationEngine.generateAIRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:407:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationEngine.generateRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:93:39)\n    at async RecommendationGenerator.generateDailyRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:90:36)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:16:13)","timestamp":"2025-08-15T02:50:10.075Z"}
{"level":"error","message":"Failed to generate recommendations for user b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba: topSpending.total_amount.toFixed is not a function","service":"finance-manager-api","stack":"TypeError: topSpending.total_amount.toFixed is not a function\n    at RecommendationEngine.generateFallbackRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:492:69)\n    at RecommendationEngine.generateAIRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:407:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationEngine.generateRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:93:39)\n    at async RecommendationGenerator.generateDailyRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:90:36)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:16:13)","timestamp":"2025-08-15T02:50:10.076Z"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:76:24","timestamp":"2025-08-15T06:33:56.959Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\recommendations.js:53:24","timestamp":"2025-08-15T06:33:57.162Z","url":"/api/recommendations","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T06:39:34.255Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\recommendations.js:53:24","timestamp":"2025-08-15T06:39:34.260Z","url":"/api/recommendations","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T06:42:31.703Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\recommendations.js:53:24","timestamp":"2025-08-15T06:42:31.708Z","url":"/api/recommendations","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T13:33:19.328Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\recommendations.js:53:24","timestamp":"2025-08-15T13:33:19.395Z","url":"/api/recommendations","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T13:39:24.714Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T13:57:34.857Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T14:01:22.509Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:80:24","timestamp":"2025-08-15T14:05:22.510Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:92:24","timestamp":"2025-08-15T14:18:57.873Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"body":{},"level":"error","message":"Error: invalid input syntax for type integer: \"high\"","method":"GET","service":"finance-manager-api","stack":"error: invalid input syntax for type integer: \"high\"\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\routes\\goals.js:81:24","timestamp":"2025-08-15T14:42:23.886Z","url":"/api/goals","userId":"b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba"}
{"code":"42803","file":"parse_agg.c","length":132,"level":"error","line":"599","message":"Recommendation cleanup failed: aggregate functions are not allowed in RETURNING","name":"error","position":"254","routine":"check_agglevels_and_constraints","service":"finance-manager-api","severity":"ERROR","stack":"error: aggregate functions are not allowed in RETURNING\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationGenerator.cleanupExpiredRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:250:35)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:26:13)","timestamp":"2025-08-18T07:02:21.194Z"}
{"level":"error","message":"Failed to generate AI recommendations: fetch failed","service":"finance-manager-api","stack":"Error: fetch failed\n    at V2.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:247:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\node_modules\\cohere-ai\\api\\resources\\v2\\client\\Client.js:31:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-18T07:02:23.558Z"}
{"level":"error","message":"Failed to generate recommendations: topSpending.total_amount.toFixed is not a function","service":"finance-manager-api","stack":"TypeError: topSpending.total_amount.toFixed is not a function\n    at RecommendationEngine.generateFallbackRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:492:69)\n    at RecommendationEngine.generateAIRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:407:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationEngine.generateRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:93:39)\n    at async RecommendationGenerator.generateDailyRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:90:36)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:16:13)","timestamp":"2025-08-18T07:02:23.565Z"}
{"level":"error","message":"Failed to generate recommendations for user b8e1d587-0eb3-4f34-bcbe-7ce6263a3cba: topSpending.total_amount.toFixed is not a function","service":"finance-manager-api","stack":"TypeError: topSpending.total_amount.toFixed is not a function\n    at RecommendationEngine.generateFallbackRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:492:69)\n    at RecommendationEngine.generateAIRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:407:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RecommendationEngine.generateRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\services\\recommendationEngine.js:93:39)\n    at async RecommendationGenerator.generateDailyRecommendations (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:90:36)\n    at async CronJob.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\project1\\workers\\recommendationGenerator.js:16:13)","timestamp":"2025-08-18T07:02:23.567Z"}
