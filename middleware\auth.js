const jwt = require('jsonwebtoken');
const { resilientQuery } = require('../config/database');
const logger = require('../utils/logger');

const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        logger.warn('Authentication failed: No token provided');
        return res.status(401).json({ error: 'Access token required' });
    }

    try {
        // Validate JWT secret exists
        if (!process.env.JWT_SECRET) {
            logger.error('JWT_SECRET not configured');
            return res.status(500).json({ error: 'Server configuration error' });
        }

        // First verify the JWT token without database call
        let decoded;
        try {
            decoded = jwt.verify(token, process.env.JWT_SECRET, {
                issuer: 'finance-manager-api',
                audience: 'finance-manager-app'
            });
        } catch (jwtError) {
            if (jwtError.name === 'TokenExpiredError') {
                logger.warn('Authentication failed: Token expired');
                return res.status(401).json({ 
                    error: 'Token expired',
                    code: 'TOKEN_EXPIRED'
                });
            } else if (jwtError.name === 'JsonWebTokenError') {
                logger.warn('Authentication failed: Invalid token');
                return res.status(401).json({ 
                    error: 'Invalid token',
                    code: 'INVALID_TOKEN'
                });
            } else {
                logger.error('JWT verification failed:', jwtError);
                return res.status(403).json({ 
                    error: 'Token verification failed',
                    code: 'VERIFICATION_FAILED'
                });
            }
        }

        // Now verify user exists in database using resilient query
        let user = null;
        
        try {
            logger.debug(`Verifying user in database: ${decoded.userId}`);
            
            const result = await resilientQuery(
                'SELECT id, email, subscription_tier, subscription_expires_at FROM users WHERE id = $1',
                [decoded.userId]
            );

            if (result.rows.length === 0) {
                logger.warn(`Authentication failed: User not found for ID: ${decoded.userId}`);
                return res.status(401).json({ error: 'User not found' });
            }

            user = result.rows[0];
            logger.debug(`User verification successful for: ${user.email}`);

        } catch (dbError) {
            logger.error('Database query failed during authentication:', {
                error: dbError.message,
                userId: decoded.userId,
                code: dbError.code
            });

            // Graceful fallback: proceed with token data only if database is unavailable
            if (decoded.userId && decoded.email) {
                logger.warn('Database unavailable, proceeding with token data only');
                user = {
                    id: decoded.userId,
                    email: decoded.email,
                    subscription_tier: decoded.subscription_tier || 'free',
                    subscription_expires_at: null
                };
            } else {
                logger.error('Database connection failed and insufficient token data');
                return res.status(503).json({ 
                    error: 'Database service temporarily unavailable',
                    code: 'DATABASE_UNAVAILABLE',
                    message: 'Please try again in a few moments'
                });
            }
        }

        // Check if premium subscription is expired (only if we have database data)
        if (user.subscription_tier === 'premium' && user.subscription_expires_at && user.subscription_expires_at < new Date()) {
            logger.info(`Premium subscription expired for user: ${user.id}`);
            
            // Try to update user to free tier, but don't fail if database is unavailable
            try {
                await resilientQuery(
                    'UPDATE users SET subscription_tier = $1 WHERE id = $2',
                    ['free', user.id]
                );
                user.subscription_tier = 'free';
            } catch (updateError) {
                logger.warn('Failed to update expired subscription, continuing with free tier assumption:', updateError);
                user.subscription_tier = 'free';
            }
        }

        // Set user data for downstream middleware
        req.user = user;
        req.userId = decoded.userId;
        
        logger.debug(`Authentication successful for user: ${user.email || decoded.email}`);
        next();

    } catch (error) {
        logger.error('Unexpected authentication error:', {
            error: error.message,
            stack: error.stack,
            userId: req.userId
        });
        
        return res.status(500).json({ 
            error: 'Authentication service error',
            code: 'AUTH_SERVICE_ERROR'
        });
    }
};

const requirePremium = (feature) => {
    return async (req, res, next) => {
        const user = req.user;
        
        if (!user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        
        if (user.subscription_tier !== 'premium' || 
            (user.subscription_expires_at && user.subscription_expires_at < new Date())) {
            return res.status(403).json({
                error: 'Premium subscription required',
                feature: feature,
                current_tier: user.subscription_tier,
                upgrade_url: '/api/user/upgrade-premium'
            });
        }
        
        next();
    };
};

module.exports = {
    authenticateToken,
    requirePremium
};