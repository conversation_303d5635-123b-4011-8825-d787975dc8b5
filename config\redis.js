const logger = require('../utils/logger');

// Mock Redis implementation for development
class MockRedisClient {
    constructor() {
        this.data = new Map();
        this.connected = false;
        logger.info('Using Mock Redis implementation for development');
    }

    async connect() {
        this.connected = true;
        logger.info('Mock Redis connected successfully');
        return this;
    }

    async ping() {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        return 'PONG';
    }

    async get(key) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const item = this.data.get(key);
        if (!item) return null;
        
        // Check if expired
        if (item.expiresAt && Date.now() > item.expiresAt) {
            this.data.delete(key);
            return null;
        }
        
        return item.value;
    }

    async set(key, value) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        this.data.set(key, { value, expiresAt: null });
        return 'OK';
    }

    async setEx(key, seconds, value) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const expiresAt = Date.now() + (seconds * 1000);
        this.data.set(key, { value, expiresAt });
        return 'OK';
    }

    async del(key) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const existed = this.data.has(key);
        this.data.delete(key);
        return existed ? 1 : 0;
    }

    async exists(key) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const item = this.data.get(key);
        if (!item) return 0;
        
        // Check if expired
        if (item.expiresAt && Date.now() > item.expiresAt) {
            this.data.delete(key);
            return 0;
        }
        
        return 1;
    }

    async expire(key, seconds) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const item = this.data.get(key);
        if (!item) return 0;
        
        item.expiresAt = Date.now() + (seconds * 1000);
        return 1;
    }

    async ttl(key) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const item = this.data.get(key);
        if (!item) return -2;
        
        if (!item.expiresAt) return -1;
        
        const remaining = Math.ceil((item.expiresAt - Date.now()) / 1000);
        return remaining > 0 ? remaining : -2;
    }

    async flushAll() {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        this.data.clear();
        return 'OK';
    }

    async keys(pattern) {
        if (!this.connected) {
            throw new Error('Mock Redis not connected');
        }
        
        const keys = Array.from(this.data.keys());
        
        if (pattern === '*') {
            return keys;
        }
        
        // Simple pattern matching (only supports * wildcard)
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return keys.filter(key => regex.test(key));
    }

    // Event emitter methods for compatibility
    on(event, callback) {
        // Mock implementation - just log
        logger.debug(`Mock Redis event listener added: ${event}`);
        return this;
    }

    emit(event, ...args) {
        // Mock implementation - just log
        logger.debug(`Mock Redis event emitted: ${event}`, args);
        return this;
    }

    // Cleanup expired keys periodically
    startCleanup() {
        setInterval(() => {
            const now = Date.now();
            for (const [key, item] of this.data.entries()) {
                if (item.expiresAt && now > item.expiresAt) {
                    this.data.delete(key);
                }
            }
        }, 60000); // Clean up every minute
    }

    // Get stats for monitoring
    getStats() {
        const now = Date.now();
        let activeKeys = 0;
        let expiredKeys = 0;
        
        for (const [key, item] of this.data.entries()) {
            if (item.expiresAt && now > item.expiresAt) {
                expiredKeys++;
            } else {
                activeKeys++;
            }
        }
        
        return {
            total_keys: this.data.size,
            active_keys: activeKeys,
            expired_keys: expiredKeys,
            memory_usage: JSON.stringify(Array.from(this.data.entries())).length
        };
    }
}

let client = null;

const connectRedis = async () => {
    try {
        // Check if Redis is available
        const useRealRedis = process.env.REDIS_HOST && process.env.REDIS_HOST !== 'localhost';
        
        if (useRealRedis) {
            // Try to connect to real Redis
            const redis = require('redis');
            client = redis.createClient({
                host: process.env.REDIS_HOST,
                port: process.env.REDIS_PORT || 6379,
                password: process.env.REDIS_PASSWORD,
            });

            client.on('error', (err) => {
                logger.error('Redis Client Error:', err);
                // Fall back to mock Redis
                logger.info('Falling back to Mock Redis implementation');
                client = new MockRedisClient();
                client.startCleanup();
            });

            client.on('connect', () => {
                logger.info('Redis connected successfully');
            });

            await client.connect();
        } else {
            // Use mock Redis for development
            client = new MockRedisClient();
            await client.connect();
            client.startCleanup();
        }
        
        return client;
    } catch (error) {
        logger.warn('Redis connection failed, using mock implementation:', error.message);
        
        // Fall back to mock Redis
        client = new MockRedisClient();
        await client.connect();
        client.startCleanup();
        
        return client;
    }
};

const getRedisClient = () => client;

module.exports = {
    connectRedis,
    getRedisClient
};