const { pool } = require('../config/database');
const recommendationEngine = require('../services/recommendationEngine');
const notificationService = require('../services/notificationService');
const logger = require('../utils/logger');
const cron = require('cron');

class RecommendationGenerator {
    constructor() {
        this.isRunning = false;
        this.jobs = [];
    }

    start() {
        // Generate recommendations daily at 8 AM
        const dailyRecommendations = new cron.CronJob('0 0 8 * * *', async () => {
            await this.generateDailyRecommendations();
        });

        // Generate weekly insights on Sunday at 10 AM
        const weeklyInsights = new cron.CronJob('0 0 10 * * 0', async () => {
            await this.generateWeeklyInsights();
        });

        // Clean up expired recommendations daily at 2 AM
        const cleanupJob = new cron.CronJob('0 0 2 * * *', async () => {
            await this.cleanupExpiredRecommendations();
        });

        this.jobs = [dailyRecommendations, weeklyInsights, cleanupJob];
        this.jobs.forEach(job => job.start());

        logger.info('Recommendation generator started with scheduled jobs');
    }

    stop() {
        this.jobs.forEach(job => job.stop());
        logger.info('Recommendation generator stopped');
    }

    // Generate daily recommendations for all users
    async generateDailyRecommendations() {
        if (this.isRunning) {
            logger.info('Recommendation generation already in progress, skipping');
            return;
        }

        this.isRunning = true;
        
        try {
            logger.info('Starting daily recommendation generation');

            // Get all active users
            const usersQuery = `
                SELECT DISTINCT u.id, u.subscription_tier
                FROM users u
                WHERE u.created_at <= NOW() - INTERVAL '7 days' -- Only users with at least 7 days of data
                AND EXISTS (
                    SELECT 1 FROM transactions t 
                    WHERE t.user_id = u.id 
                    AND t.created_at >= NOW() - INTERVAL '30 days'
                )
            `;

            const usersResult = await pool.query(usersQuery);
            const users = usersResult.rows;

            logger.info(`Generating recommendations for ${users.length} users`);

            let successCount = 0;
            let errorCount = 0;

            for (const user of users) {
                try {
                    // Check if user already has recent recommendations
                    const recentRecsQuery = `
                        SELECT COUNT(*) FROM ai_recommendations 
                        WHERE user_id = $1 
                        AND created_at >= CURRENT_DATE
                    `;
                    
                    const recentRecsResult = await pool.query(recentRecsQuery, [user.id]);
                    const todayRecommendations = parseInt(recentRecsResult.rows[0].count);

                    // Skip if user already has recommendations today
                    if (todayRecommendations > 0) {
                        continue;
                    }

                    // Generate recommendations
                    const result = await recommendationEngine.generateRecommendations(user.id);

                    if (result.recommendations.length > 0) {
                        // Send notifications for high-priority recommendations
                        const highPriorityRecs = result.recommendations.filter(rec => rec.priority >= 4);
                        
                        if (highPriorityRecs.length > 0) {
                            await notificationService.sendBulkNotifications(highPriorityRecs, 'in_app');
                            
                            // Send push notifications for premium users
                            if (user.subscription_tier === 'premium') {
                                await notificationService.sendBulkNotifications(
                                    highPriorityRecs.slice(0, 2), // Limit to 2 push notifications
                                    'push'
                                );
                            }
                        }

                        successCount++;
                        logger.info(`Generated ${result.recommendations.length} recommendations for user: ${user.id}`);
                    }

                    // Add delay to avoid overwhelming the AI service
                    await this.delay(1000);

                } catch (error) {
                    errorCount++;
                    logger.error(`Failed to generate recommendations for user ${user.id}:`, error);
                }
            }

            logger.info(`Daily recommendation generation completed: ${successCount} successful, ${errorCount} errors`);

        } catch (error) {
            logger.error('Daily recommendation generation failed:', error);
        } finally {
            this.isRunning = false;
        }
    }

    // Generate weekly insights for premium users
    async generateWeeklyInsights() {
        try {
            logger.info('Starting weekly insights generation');

            // Get premium users
            const premiumUsersQuery = `
                SELECT id FROM users 
                WHERE subscription_tier = 'premium'
                AND (subscription_expires_at IS NULL OR subscription_expires_at > NOW())
            `;

            const premiumUsersResult = await pool.query(premiumUsersQuery);
            const premiumUsers = premiumUsersResult.rows;

            for (const user of premiumUsers) {
                try {
                    // Generate comprehensive weekly insights
                    await this.generateWeeklyInsightForUser(user.id);
                    
                    // Add delay between users
                    await this.delay(2000);

                } catch (error) {
                    logger.error(`Failed to generate weekly insights for user ${user.id}:`, error);
                }
            }

            logger.info(`Weekly insights generated for ${premiumUsers.length} premium users`);

        } catch (error) {
            logger.error('Weekly insights generation failed:', error);
        }
    }

    // Generate weekly insight for specific user
    async generateWeeklyInsightForUser(userId) {
        try {
            // Get weekly spending summary
            const weeklySpendingQuery = `
                SELECT 
                    SUM(amount) as total_spent,
                    COUNT(*) as transaction_count,
                    COUNT(DISTINCT category_id) as categories_used
                FROM transactions
                WHERE user_id = $1 
                AND type = 'expense'
                AND transaction_date >= CURRENT_DATE - INTERVAL '7 days'
            `;

            const weeklySpendingResult = await pool.query(weeklySpendingQuery, [userId]);
            const weeklyData = weeklySpendingResult.rows[0];

            // Get comparison with previous week
            const previousWeekQuery = `
                SELECT SUM(amount) as previous_week_spent
                FROM transactions
                WHERE user_id = $1 
                AND type = 'expense'
                AND transaction_date >= CURRENT_DATE - INTERVAL '14 days'
                AND transaction_date < CURRENT_DATE - INTERVAL '7 days'
            `;

            const previousWeekResult = await pool.query(previousWeekQuery, [userId]);
            const previousWeekSpent = parseFloat(previousWeekResult.rows[0].previous_week_spent || 0);
            const currentWeekSpent = parseFloat(weeklyData.total_spent || 0);

            const weeklyChange = previousWeekSpent > 0 ? 
                ((currentWeekSpent - previousWeekSpent) / previousWeekSpent) * 100 : 0;

            // Create weekly insight recommendation
            const insightTitle = this.generateWeeklyInsightTitle(weeklyChange, currentWeekSpent);
            const insightDescription = this.generateWeeklyInsightDescription(
                weeklyData, 
                currentWeekSpent, 
                previousWeekSpent, 
                weeklyChange
            );

            // Store as a special insight recommendation
            await pool.query(`
                INSERT INTO ai_recommendations (
                    user_id, type, category, title, description, action_text,
                    potential_savings, confidence_score, priority, data_context, expires_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            `, [
                userId,
                'behavioral_insight',
                'weekly_summary',
                insightTitle,
                insightDescription,
                'Review your weekly spending patterns',
                0,
                0.9,
                3,
                JSON.stringify({
                    type: 'weekly_insight',
                    current_week_spent: currentWeekSpent,
                    previous_week_spent: previousWeekSpent,
                    weekly_change: weeklyChange,
                    transaction_count: parseInt(weeklyData.transaction_count),
                    categories_used: parseInt(weeklyData.categories_used)
                }),
                new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days
            ]);

            logger.info(`Weekly insight generated for user: ${userId}`);

        } catch (error) {
            logger.error(`Failed to generate weekly insight for user ${userId}:`, error);
            throw error;
        }
    }

    // Clean up expired recommendations
    async cleanupExpiredRecommendations() {
        try {
            logger.info('Starting recommendation cleanup');

            // Mark expired recommendations as expired
            const expiredResult = await pool.query(`
                UPDATE ai_recommendations 
                SET status = 'expired', updated_at = NOW()
                WHERE status = 'active' 
                AND expires_at IS NOT NULL 
                AND expires_at < NOW()
                RETURNING COUNT(*)
            `);

            const expiredCount = expiredResult.rowCount;

            // Clean up old dismissed/expired recommendations (older than 90 days)
            const cleanupResult = await pool.query(`
                DELETE FROM ai_recommendations 
                WHERE status IN ('dismissed', 'expired')
                AND updated_at < NOW() - INTERVAL '90 days'
                RETURNING COUNT(*)
            `);

            const cleanedCount = cleanupResult.rowCount;

            // Clean up old notifications (older than 30 days)
            const notificationCleanup = await pool.query(`
                DELETE FROM notifications 
                WHERE created_at < NOW() - INTERVAL '30 days'
                AND status IN ('read', 'clicked', 'failed')
                RETURNING COUNT(*)
            `);

            const notificationsCleanedCount = notificationCleanup.rowCount;

            logger.info(`Cleanup completed: ${expiredCount} expired, ${cleanedCount} old recommendations deleted, ${notificationsCleanedCount} old notifications deleted`);

        } catch (error) {
            logger.error('Recommendation cleanup failed:', error);
        }
    }

    // Helper methods
    generateWeeklyInsightTitle(weeklyChange, currentWeekSpent) {
        if (weeklyChange > 20) {
            return `Spending Alert: ${weeklyChange.toFixed(1)}% increase this week`;
        } else if (weeklyChange < -15) {
            return `Great Job! ${Math.abs(weeklyChange).toFixed(1)}% spending reduction`;
        } else if (currentWeekSpent > 5000) {
            return `Weekly Review: ₹${currentWeekSpent.toLocaleString('en-IN')} spent`;
        } else {
            return 'Your Weekly Spending Summary';
        }
    }

    generateWeeklyInsightDescription(weeklyData, currentWeek, previousWeek, change) {
        const transactionCount = parseInt(weeklyData.transaction_count);
        const categoriesUsed = parseInt(weeklyData.categories_used);
        
        let description = `This week you spent ₹${currentWeek.toLocaleString('en-IN')} across ${transactionCount} transactions in ${categoriesUsed} categories. `;

        if (change > 10) {
            description += `This is ${change.toFixed(1)}% higher than last week. Consider reviewing your recent purchases.`;
        } else if (change < -10) {
            description += `This is ${Math.abs(change).toFixed(1)}% lower than last week. Keep up the good work!`;
        } else {
            description += `This is similar to last week's spending pattern.`;
        }

        return description;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create and export singleton instance
const recommendationGenerator = new RecommendationGenerator();

module.exports = recommendationGenerator;