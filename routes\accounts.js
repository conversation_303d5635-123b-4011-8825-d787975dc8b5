const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { validateAccount } = require('../middleware/validation');
const { pool } = require('../config/database');
const analyticsService = require('../services/analyticsService');
const currencyService = require('../services/currencyService');
const logger = require('../utils/logger');

const router = express.Router();

// Get all accounts for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { include_balance_history } = req.query;

        let query = `
            SELECT 
                a.*,
                (SELECT COUNT(*) FROM transactions WHERE account_id = a.id) as transaction_count
            FROM accounts a
            WHERE a.user_id = $1 AND a.is_active = true
            ORDER BY a.created_at ASC
        `;

        const result = await pool.query(query, [userId]);
        const accounts = result.rows;

        // Convert balances to user's preferred currency if needed
        for (let account of accounts) {
            account.balance = parseFloat(account.balance);
            account.transaction_count = parseInt(account.transaction_count);

            if (include_balance_history === 'true') {
                account.balance_history = await analyticsService.getAccountBalanceHistory(
                    userId, 
                    account.id, 
                    30
                );
            }
        }

        res.json({ accounts });
    } catch (error) {
        next(error);
    }
});

// Get single account
router.get('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;

        const result = await pool.query(
            'SELECT * FROM accounts WHERE id = $1 AND user_id = $2 AND is_active = true',
            [accountId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        const account = result.rows[0];
        account.balance = parseFloat(account.balance);

        res.json({ account });
    } catch (error) {
        next(error);
    }
});

// Create account
router.post('/', authenticateToken, validateAccount, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { name, type, balance = 0, currency = 'USD' } = req.body;

        const result = await pool.query(`
            INSERT INTO accounts (user_id, name, type, balance, currency)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `, [userId, name, type, balance, currency]);

        const account = result.rows[0];
        account.balance = parseFloat(account.balance);

        logger.info(`Account created: ${account.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Account created successfully',
            account
        });
    } catch (error) {
        next(error);
    }
});

// Update account
router.put('/:id', authenticateToken, validateAccount, async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;
        const { name, type, currency } = req.body;

        const result = await pool.query(`
            UPDATE accounts 
            SET name = $3, type = $4, currency = $5
            WHERE id = $1 AND user_id = $2 AND is_active = true
            RETURNING *
        `, [accountId, userId, name, type, currency]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        const account = result.rows[0];
        account.balance = parseFloat(account.balance);

        logger.info(`Account updated: ${accountId} for user: ${userId}`);

        res.json({
            message: 'Account updated successfully',
            account
        });
    } catch (error) {
        next(error);
    }
});

// Soft delete account
router.delete('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;

        // Check if account has transactions
        const transactionCheck = await pool.query(
            'SELECT COUNT(*) FROM transactions WHERE account_id = $1',
            [accountId]
        );

        const transactionCount = parseInt(transactionCheck.rows[0].count);

        if (transactionCount > 0) {
            // Soft delete (deactivate) if has transactions
            const result = await pool.query(
                'UPDATE accounts SET is_active = false WHERE id = $1 AND user_id = $2 RETURNING *',
                [accountId, userId]
            );

            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Account not found' });
            }

            logger.info(`Account deactivated: ${accountId} for user: ${userId}`);

            res.json({ 
                message: 'Account deactivated successfully (has existing transactions)',
                deactivated: true
            });
        } else {
            // Hard delete if no transactions
            const result = await pool.query(
                'DELETE FROM accounts WHERE id = $1 AND user_id = $2 RETURNING *',
                [accountId, userId]
            );

            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Account not found' });
            }

            logger.info(`Account deleted: ${accountId} for user: ${userId}`);

            res.json({ 
                message: 'Account deleted successfully',
                deleted: true
            });
        }
    } catch (error) {
        next(error);
    }
});

// Get account balance history
router.get('/:id/balance-history', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;
        const { days = 30 } = req.query;

        // Verify account ownership
        const accountCheck = await pool.query(
            'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
            [accountId, userId]
        );

        if (accountCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        const balanceHistory = await analyticsService.getAccountBalanceHistory(
            userId, 
            accountId, 
            parseInt(days)
        );

        res.json({
            account_id: accountId,
            days: parseInt(days),
            balance_history: balanceHistory
        });
    } catch (error) {
        next(error);
    }
});

// Get account summary
router.get('/:id/summary', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;
        const { period = 'month' } = req.query;

        // Verify account ownership
        const accountResult = await pool.query(
            'SELECT * FROM accounts WHERE id = $1 AND user_id = $2',
            [accountId, userId]
        );

        if (accountResult.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        const account = accountResult.rows[0];

        // Calculate date range based on period
        let startDate;
        const endDate = new Date();
        
        switch (period) {
            case 'week':
                startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                break;
            case 'year':
                startDate = new Date(endDate.getFullYear(), 0, 1);
                break;
            default:
                startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        }

        // Get transaction summary
        const summaryQuery = `
            SELECT 
                COUNT(*) as total_transactions,
                SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expenses,
                AVG(CASE WHEN type = 'expense' THEN amount END) as avg_expense,
                MAX(CASE WHEN type = 'expense' THEN amount END) as max_expense
            FROM transactions 
            WHERE account_id = $1 
            AND transaction_date BETWEEN $2 AND $3
        `;

        const summaryResult = await pool.query(summaryQuery, [accountId, startDate, endDate]);
        const summary = summaryResult.rows[0];

        // Get top categories
        const categoriesQuery = `
            SELECT 
                c.name,
                c.color,
                SUM(t.amount) as total_amount,
                COUNT(t.id) as transaction_count
            FROM transactions t
            JOIN categories c ON t.category_id = c.id
            WHERE t.account_id = $1 
            AND t.transaction_date BETWEEN $2 AND $3
            AND t.type = 'expense'
            GROUP BY c.id, c.name, c.color
            ORDER BY total_amount DESC
            LIMIT 5
        `;

        const categoriesResult = await pool.query(categoriesQuery, [accountId, startDate, endDate]);

        res.json({
            account: {
                id: account.id,
                name: account.name,
                type: account.type,
                balance: parseFloat(account.balance),
                currency: account.currency
            },
            period,
            date_range: {
                start: startDate,
                end: endDate
            },
            summary: {
                total_transactions: parseInt(summary.total_transactions),
                total_income: parseFloat(summary.total_income || 0),
                total_expenses: parseFloat(summary.total_expenses || 0),
                net_amount: parseFloat(summary.total_income || 0) - parseFloat(summary.total_expenses || 0),
                avg_expense: parseFloat(summary.avg_expense || 0),
                max_expense: parseFloat(summary.max_expense || 0)
            },
            top_categories: categoriesResult.rows.map(row => ({
                name: row.name,
                color: row.color,
                total_amount: parseFloat(row.total_amount),
                transaction_count: parseInt(row.transaction_count)
            }))
        });
    } catch (error) {
        next(error);
    }
});

// Get account summary grouped by currency
router.get('/currency-summary', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const query = `
            SELECT
                currency,
                COUNT(*) as account_count,
                SUM(balance) as total_balance,
                AVG(balance) as avg_balance
            FROM accounts
            WHERE user_id = $1 AND is_active = true
            GROUP BY currency
            ORDER BY total_balance DESC
        `;

        const result = await pool.query(query, [userId]);

        const summary = result.rows.map(row => ({
            currency: row.currency,
            account_count: parseInt(row.account_count),
            total_balance: parseFloat(row.total_balance),
            avg_balance: parseFloat(row.avg_balance)
        }));

        res.json({ currency_summary: summary });
    } catch (error) {
        next(error);
    }
});

module.exports = router;