const awsService = require('../config/aws');
const logger = require('../utils/logger');

class PDFProcessor {
    constructor() {
        this.name = 'PDF Processor';
    }

    async process(fileBuffer, s3Key) {
        try {
            logger.info('Processing PDF file with AWS Textract');
            
            if (!s3Key) {
                throw new Error('S3 key required for PDF processing');
            }

            // Use AWS Textract to extract text and tables
            const textractResult = await awsService.extractTextFromPDF(s3Key);
            
            // Parse Textract response
            const extractedData = this.parseTextractResponse(textractResult);
            
            // Convert to normalized transactions
            const transactions = this.extractTransactions(extractedData);
            
            return {
                success: true,
                transactions: transactions,
                totalCount: transactions.length,
                rawData: extractedData.tables.slice(0, 3), // Keep sample tables
                extractedText: extractedData.text.substring(0, 1000) // Keep sample text
            };

        } catch (error) {
            logger.error('PDF processing failed:', error);
            throw new Error(`PDF processing failed: ${error.message}`);
        }
    }

    parseTextractResponse(textractResult) {
        const blocks = textractResult.Blocks || [];
        
        const lines = [];
        const tables = [];
        let currentTable = null;
        
        // Group blocks by type
        const lineBlocks = blocks.filter(block => block.BlockType === 'LINE');
        const tableBlocks = blocks.filter(block => block.BlockType === 'TABLE');
        const cellBlocks = blocks.filter(block => block.BlockType === 'CELL');
        
        // Extract text lines
        lineBlocks.forEach(block => {
            if (block.Text) {
                lines.push(block.Text.trim());
            }
        });
        
        // Extract tables
        tableBlocks.forEach(tableBlock => {
            const table = this.extractTable(tableBlock, cellBlocks, blocks);
            if (table && table.length > 0) {
                tables.push(table);
            }
        });
        
        return {
            text: lines.join('\n'),
            lines: lines,
            tables: tables
        };
    }

    extractTable(tableBlock, cellBlocks, allBlocks) {
        const tableCells = cellBlocks.filter(cell => 
            cell.Relationships && 
            cell.Relationships.some(rel => 
                rel.Type === 'CHILD' && 
                tableBlock.Relationships &&
                tableBlock.Relationships.some(tableRel => 
                    tableRel.Type === 'CHILD' && 
                    tableRel.Ids.includes(cell.Id)
                )
            )
        );
        
        // Group cells by row and column
        const rows = {};
        
        tableCells.forEach(cell => {
            const rowIndex = cell.RowIndex || 1;
            const colIndex = cell.ColumnIndex || 1;
            
            if (!rows[rowIndex]) {
                rows[rowIndex] = {};
            }
            
            // Extract cell text
            const cellText = this.extractCellText(cell, allBlocks);
            rows[rowIndex][colIndex] = cellText;
        });
        
        // Convert to array format
        const tableArray = [];
        Object.keys(rows).sort((a, b) => parseInt(a) - parseInt(b)).forEach(rowIndex => {
            const row = rows[rowIndex];
            const rowArray = [];
            
            const maxCol = Math.max(...Object.keys(row).map(k => parseInt(k)));
            for (let i = 1; i <= maxCol; i++) {
                rowArray.push(row[i] || '');
            }
            
            tableArray.push(rowArray);
        });
        
        return tableArray;
    }

    extractCellText(cell, allBlocks) {
        if (!cell.Relationships) return '';
        
        const wordRelations = cell.Relationships.filter(rel => rel.Type === 'CHILD');
        if (wordRelations.length === 0) return '';
        
        const words = [];
        wordRelations.forEach(rel => {
            rel.Ids.forEach(id => {
                const wordBlock = allBlocks.find(block => block.Id === id);
                if (wordBlock && wordBlock.Text) {
                    words.push(wordBlock.Text);
                }
            });
        });
        
        return words.join(' ').trim();
    }

    extractTransactions(extractedData) {
        const transactions = [];
        
        // Try to extract from tables first
        for (const table of extractedData.tables) {
            const tableTransactions = this.extractTransactionsFromTable(table);
            transactions.push(...tableTransactions);
        }
        
        // If no table transactions found, try text parsing
        if (transactions.length === 0) {
            const textTransactions = this.extractTransactionsFromText(extractedData.lines);
            transactions.push(...textTransactions);
        }
        
        return transactions.filter(tx => tx !== null);
    }

    extractTransactionsFromTable(table) {
        if (!table || table.length < 2) return [];
        
        const transactions = [];
        const headers = table[0].map(h => h.toLowerCase().trim());
        
        // Find column indices
        const dateIndex = this.findColumnIndex(headers, ['date', 'trans date', 'posting date']);
        const descIndex = this.findColumnIndex(headers, ['description', 'memo', 'payee', 'merchant']);
        const amountIndex = this.findColumnIndex(headers, ['amount', 'debit', 'credit']);
        const debitIndex = this.findColumnIndex(headers, ['debit', 'withdrawal']);
        const creditIndex = this.findColumnIndex(headers, ['credit', 'deposit']);
        
        if (dateIndex === -1 || descIndex === -1 || (amountIndex === -1 && (debitIndex === -1 || creditIndex === -1))) {
            logger.warn('Could not identify required columns in PDF table');
            return [];
        }
        
        // Process data rows
        for (let i = 1; i < table.length; i++) {
            const row = table[i];
            
            try {
                let amount = 0;
                if (amountIndex !== -1) {
                    amount = this.parseAmount(row[amountIndex]);
                } else {
                    const debit = this.parseAmount(row[debitIndex] || '0');
                    const credit = this.parseAmount(row[creditIndex] || '0');
                    amount = credit > 0 ? credit : -debit;
                }
                
                if (amount === 0) continue; // Skip zero amount transactions
                
                const transaction = {
                    originalIndex: i - 1,
                    date: this.parseDate(row[dateIndex]),
                    description: this.cleanDescription(row[descIndex]),
                    amount: amount,
                    type: amount >= 0 ? 'income' : 'expense',
                    category: null,
                    merchant: null,
                    reference: null
                };
                
                if (transaction.date && transaction.description) {
                    transactions.push(transaction);
                }
                
            } catch (error) {
                logger.warn(`Failed to parse PDF table row ${i}:`, error);
            }
        }
        
        return transactions;
    }

    extractTransactionsFromText(lines) {
        const transactions = [];
        
        // Look for transaction patterns in text
        const transactionPattern = /(\d{1,2}\/\d{1,2}\/\d{2,4})\s+(.+?)\s+([\-\$\d,\.]+)/g;
        
        const fullText = lines.join('\n');
        let match;
        let index = 0;
        
        while ((match = transactionPattern.exec(fullText)) !== null) {
            try {
                const [, dateStr, description, amountStr] = match;
                
                const transaction = {
                    originalIndex: index++,
                    date: this.parseDate(dateStr),
                    description: this.cleanDescription(description),
                    amount: this.parseAmount(amountStr),
                    type: this.parseAmount(amountStr) >= 0 ? 'income' : 'expense',
                    category: null,
                    merchant: null,
                    reference: null
                };
                
                if (transaction.date && transaction.description && transaction.amount !== 0) {
                    transactions.push(transaction);
                }
                
            } catch (error) {
                logger.warn('Failed to parse text transaction:', error);
            }
        }
        
        return transactions;
    }

    findColumnIndex(headers, searchTerms) {
        for (const term of searchTerms) {
            const index = headers.findIndex(h => h.includes(term));
            if (index !== -1) return index;
        }
        return -1;
    }

    cleanDescription(description) {
        if (!description) return 'Unknown Transaction';
        
        return description
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\-\.\,\&]/g, '')
            .trim()
            .substring(0, 255);
    }

    parseAmount(amount) {
        if (typeof amount === 'number') return amount;
        if (!amount) return 0;
        
        const cleanAmount = amount.toString()
            .replace(/[$,\s]/g, '')
            .replace(/[()]/g, '');
        
        const parsed = parseFloat(cleanAmount);
        return isNaN(parsed) ? 0 : parsed;
    }

    parseDate(dateStr) {
        if (!dateStr) return null;
        
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                const parts = dateStr.split('/');
                if (parts.length === 3) {
                    const [month, day, year] = parts;
                    const fullYear = year.length === 2 ? `20${year}` : year;
                    return new Date(fullYear, month - 1, day);
                }
                return null;
            }
            return date;
        } catch (error) {
            logger.warn(`Failed to parse date: ${dateStr}`);
            return null;
        }
    }
}

module.exports = PDFProcessor;