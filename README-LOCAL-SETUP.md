# 🐳 Local PostgreSQL Setup Guide

This guide will help you set up a local PostgreSQL database for development using Docker.

## Prerequisites

1. **Docker Desktop** - Download and install from [docker.com](https://www.docker.com/products/docker-desktop)
2. **Node.js 18+** - Already available in this environment

## Quick Start

### 1. Start Local Database
```bash
npm run db:start
```

This command will:
- Start PostgreSQL and Redis containers using Docker Compose
- Wait for containers to be healthy
- Display connection details

### 2. Set Up Environment
```bash
# Copy the local environment template
cp .env.local .env

# The .env file is now configured for local development
```

### 3. Initialize Database
```bash
# Run database migrations
npm run db:migrate

# Seed with test data
npm run db:seed
```

### 4. Start Development Server
```bash
npm run dev
```

## Database Connection Details

- **Host**: localhost
- **Port**: 5432
- **Database**: finance_manager
- **Username**: postgres
- **Password**: password123

## Useful Commands

### Database Management
```bash
# Check database connection
npm run db:check

# Reset database (removes all data)
npm run db:reset

# Stop database containers
npm run db:stop
```

### Docker Commands
```bash
# View container status
docker-compose ps

# View container logs
docker-compose logs postgres
docker-compose logs redis

# Connect to PostgreSQL directly
docker exec -it finance-manager-postgres psql -U postgres -d finance_manager

# Connect to Redis directly
docker exec -it finance-manager-redis redis-cli
```

## Troubleshooting

### Port Already in Use
If port 5432 is already in use:
```bash
# Check what's using the port
lsof -i :5432

# Stop any existing PostgreSQL service
sudo service postgresql stop  # Linux
brew services stop postgresql  # macOS
```

### Container Won't Start
```bash
# Check Docker is running
docker --version

# View detailed logs
docker-compose logs

# Remove containers and start fresh
docker-compose down -v
npm run db:start
```

### Connection Refused
```bash
# Wait for containers to be fully ready
npm run db:check

# Restart containers
npm run db:stop
npm run db:start
```

## Data Persistence

- Database data is stored in Docker volumes
- Data persists between container restarts
- Use `npm run db:reset` to completely reset data

## Switching to Production

When deploying to production:

1. Update `.env` with production database credentials
2. The same migration and seed scripts will work
3. No code changes needed - just environment variables

## Security Notes

- Default credentials are for local development only
- Change passwords for any shared development environments
- Never commit `.env` files with real credentials

## Performance Tips

- Keep containers running during development
- Use `npm run db:stop` only when done for the day
- Database connections are pooled for efficiency

---

**Need Help?** Check the logs with `docker-compose logs` or run `npm run db:check` to diagnose connection issues.