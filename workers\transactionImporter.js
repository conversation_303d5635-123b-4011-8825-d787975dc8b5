const { importQueue } = require('../config/queue');
const { pool } = require('../config/database');
const logger = require('../utils/logger');
const crypto = require('crypto');

// Process transaction imports
importQueue.process('import-transactions', async (job) => {
    const { sessionId, uploadId, userId, accountId, settings } = job.data;
    
    try {
        logger.info(`Starting transaction import for session: ${sessionId}`);
        
        // Update session status
        await pool.query(
            'UPDATE import_sessions SET status = $1 WHERE id = $2',
            ['processing', sessionId]
        );

        // Get processed transactions
        const uploadResult = await pool.query(
            'SELECT processed_data FROM statement_uploads WHERE id = $1',
            [uploadId]
        );
        
        if (uploadResult.rows.length === 0) {
            throw new Error('Upload not found');
        }

        const processedData = uploadResult.rows[0].processed_data;
        const transactions = processedData.transactions || [];
        
        if (transactions.length === 0) {
            throw new Error('No transactions to import');
        }

        // Get user's categories for mapping
        const categoriesResult = await pool.query(
            'SELECT id, name FROM categories WHERE user_id = $1 AND is_active = true',
            [userId]
        );
        
        const categoryMap = new Map();
        categoriesResult.rows.forEach(cat => {
            categoryMap.set(cat.name.toLowerCase(), cat.id);
        });

        let importedCount = 0;
        let skippedCount = 0;
        let duplicateCount = 0;

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            for (const transaction of transactions) {
                try {
                    // Check for duplicates if enabled
                    if (settings.skip_duplicates) {
                        const duplicateHash = generateTransactionHash(transaction);
                        const duplicateCheck = await client.query(`
                            SELECT id FROM transactions 
                            WHERE user_id = $1 
                            AND account_id = $2 
                            AND ABS(amount - $3) < 0.01 
                            AND transaction_date = $4 
                            AND description ILIKE $5
                            LIMIT 1
                        `, [
                            userId,
                            accountId,
                            Math.abs(transaction.amount),
                            transaction.date,
                            `%${transaction.description.substring(0, 50)}%`
                        ]);

                        if (duplicateCheck.rows.length > 0) {
                            duplicateCount++;
                            continue;
                        }
                    }

                    // Determine category
                    let categoryId = null;
                    
                    // Check custom mappings first
                    if (settings.category_mappings && settings.category_mappings[transaction.suggested_category]) {
                        categoryId = settings.category_mappings[transaction.suggested_category];
                    } else if (transaction.suggested_category) {
                        // Use AI suggested category
                        categoryId = categoryMap.get(transaction.suggested_category.toLowerCase());
                    }

                    // Create transaction
                    const transactionResult = await client.query(`
                        INSERT INTO transactions (
                            user_id, account_id, category_id, amount, type, description,
                            transaction_date, merchant, import_id, original_description
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                        RETURNING id
                    `, [
                        userId,
                        accountId,
                        categoryId,
                        transaction.type === 'expense' ? -Math.abs(transaction.amount) : Math.abs(transaction.amount),
                        transaction.type,
                        transaction.description,
                        transaction.date,
                        transaction.suggested_merchant || transaction.merchant,
                        sessionId,
                        transaction.description
                    ]);

                    // Update account balance
                    const balanceChange = transaction.type === 'expense' 
                        ? -Math.abs(transaction.amount) 
                        : Math.abs(transaction.amount);
                    
                    await client.query(
                        'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
                        [balanceChange, accountId]
                    );

                    importedCount++;

                    // Update progress periodically
                    if (importedCount % 10 === 0) {
                        await client.query(`
                            UPDATE import_sessions 
                            SET imported_count = $1, duplicate_count = $2 
                            WHERE id = $3
                        `, [importedCount, duplicateCount, sessionId]);
                    }

                } catch (error) {
                    logger.warn(`Failed to import transaction:`, error);
                    skippedCount++;
                }
            }

            // Final update
            await client.query(`
                UPDATE import_sessions 
                SET 
                    imported_count = $1,
                    skipped_count = $2,
                    duplicate_count = $3,
                    status = $4,
                    completed_at = NOW()
                WHERE id = $5
            `, [importedCount, skippedCount, duplicateCount, 'completed', sessionId]);

            await client.query('COMMIT');

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

        logger.info(`Transaction import completed for session: ${sessionId} (${importedCount} imported, ${skippedCount} skipped, ${duplicateCount} duplicates)`);
        
        return {
            success: true,
            sessionId: sessionId,
            importedCount: importedCount,
            skippedCount: skippedCount,
            duplicateCount: duplicateCount
        };

    } catch (error) {
        logger.error(`Transaction import failed for session ${sessionId}:`, error);
        
        // Update session status to failed
        await pool.query(`
            UPDATE import_sessions 
            SET status = $1, completed_at = NOW() 
            WHERE id = $2
        `, ['failed', sessionId]);
        
        throw error;
    }
});

// Generate hash for duplicate detection
function generateTransactionHash(transaction) {
    const hashString = `${transaction.date}_${transaction.description}_${transaction.amount}`;
    return crypto.createHash('md5').update(hashString).digest('hex');
}

logger.info('Transaction importer worker started');