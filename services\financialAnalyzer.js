const { pool } = require('../config/database');
const logger = require('../utils/logger');

class FinancialAnalyzer {
    constructor() {
        this.dataCompletenessThresholds = {
            SUFFICIENT: 0.7,
            MINIMAL: 0.3,
            INSUFFICIENT: 0.3
        };
    }

    // Build comprehensive financial profile for AI analysis
    async buildFinancialProfile(userId) {
        try {
            logger.info(`Building financial profile for user: ${userId}`);

            const [income, expenses, debt, savings, dataCompleteness] = await Promise.all([
                this.analyzeIncome(userId),
                this.analyzeExpenses(userId),
                this.analyzeDebt(userId),
                this.analyzeSavings(userId),
                this.calculateDataCompleteness(userId)
            ]);

            const profile = {
                income,
                expenses,
                debt,
                savings,
                data_completeness: dataCompleteness,
                analysis_date: new Date().toISOString(),
                recommendations: this.generateRecommendations(income, expenses, debt, savings)
            };

            logger.info(`Financial profile built for user ${userId}: ${dataCompleteness.score * 100}% data completeness`);
            return profile;

        } catch (error) {
            logger.error('Failed to build financial profile:', error);
            throw error;
        }
    }

    // Analyze user's income patterns
    async analyzeIncome(userId) {
        const query = `
            SELECT 
                DATE_TRUNC('month', transaction_date) as month,
                SUM(amount) as monthly_income,
                COUNT(*) as transaction_count,
                AVG(amount) as avg_transaction
            FROM transactions 
            WHERE user_id = $1 
            AND type = 'income'
            AND transaction_date >= CURRENT_DATE - INTERVAL '6 months'
            GROUP BY DATE_TRUNC('month', transaction_date)
            ORDER BY month DESC
        `;

        const result = await pool.query(query, [userId]);
        const monthlyData = result.rows;

        if (monthlyData.length === 0) {
            return {
                estimated_monthly: 0,
                confidence_score: 0,
                pattern: 'no_data',
                months_analyzed: 0,
                stability: 'unknown'
            };
        }

        const incomes = monthlyData.map(row => parseFloat(row.monthly_income));
        const avgIncome = incomes.reduce((sum, income) => sum + income, 0) / incomes.length;
        const stdDev = this.calculateStandardDeviation(incomes);
        const coefficientOfVariation = stdDev / avgIncome;

        // Determine income pattern
        let pattern = 'regular';
        if (coefficientOfVariation > 0.3) pattern = 'irregular';
        else if (this.isGrowingTrend(incomes)) pattern = 'growing';
        else if (this.isDecliningTrend(incomes)) pattern = 'declining';

        // Calculate confidence based on data consistency and recency
        const confidence = Math.min(1, (monthlyData.length / 6) * (1 - Math.min(coefficientOfVariation, 0.5)));

        return {
            estimated_monthly: avgIncome,
            confidence_score: confidence,
            pattern: pattern,
            months_analyzed: monthlyData.length,
            stability: coefficientOfVariation < 0.2 ? 'stable' : coefficientOfVariation < 0.4 ? 'moderate' : 'volatile',
            trend: {
                direction: pattern,
                coefficient_of_variation: coefficientOfVariation,
                standard_deviation: stdDev
            }
        };
    }

    // Analyze user's expense patterns
    async analyzeExpenses(userId) {
        const query = `
            SELECT 
                DATE_TRUNC('month', transaction_date) as month,
                SUM(amount) as monthly_expenses,
                COUNT(*) as transaction_count
            FROM transactions 
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= CURRENT_DATE - INTERVAL '6 months'
            GROUP BY DATE_TRUNC('month', transaction_date)
            ORDER BY month DESC
        `;

        const categoryQuery = `
            SELECT 
                c.name as category_name,
                SUM(t.amount) as total_amount,
                COUNT(t.id) as transaction_count,
                AVG(t.amount) as avg_amount
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.user_id = $1 
            AND t.type = 'expense'
            AND t.transaction_date >= CURRENT_DATE - INTERVAL '3 months'
            GROUP BY c.name
            ORDER BY total_amount DESC
        `;

        const [expenseResult, categoryResult] = await Promise.all([
            pool.query(query, [userId]),
            pool.query(categoryQuery, [userId])
        ]);

        const monthlyData = expenseResult.rows;
        const categoryData = categoryResult.rows;

        if (monthlyData.length === 0) {
            return {
                total_monthly: 0,
                essential: 0,
                discretionary: 0,
                categories: [],
                confidence_score: 0
            };
        }

        const expenses = monthlyData.map(row => parseFloat(row.monthly_expenses));
        const avgExpenses = expenses.reduce((sum, expense) => sum + expense, 0) / expenses.length;

        // Categorize expenses as essential vs discretionary
        const { essential, discretionary } = this.categorizeExpenses(categoryData);

        return {
            total_monthly: avgExpenses,
            essential: essential,
            discretionary: discretionary,
            categories: categoryData.map(cat => ({
                name: cat.category_name || 'Uncategorized',
                total_amount: parseFloat(cat.total_amount),
                transaction_count: parseInt(cat.transaction_count),
                avg_amount: parseFloat(cat.avg_amount),
                type: this.isEssentialCategory(cat.category_name) ? 'essential' : 'discretionary'
            })),
            confidence_score: Math.min(1, monthlyData.length / 6)
        };
    }

    // Analyze debt obligations
    async analyzeDebt(userId) {
        // Look for recurring payments that might be EMIs/debt payments
        const query = `
            SELECT 
                description,
                amount,
                COUNT(*) as frequency,
                AVG(amount) as avg_amount,
                merchant
            FROM transactions 
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= CURRENT_DATE - INTERVAL '6 months'
            AND (
                description ILIKE '%emi%' OR 
                description ILIKE '%loan%' OR 
                description ILIKE '%credit card%' OR
                description ILIKE '%mortgage%' OR
                merchant ILIKE '%bank%'
            )
            GROUP BY description, amount, merchant
            HAVING COUNT(*) >= 2
            ORDER BY frequency DESC, avg_amount DESC
        `;

        const result = await pool.query(query, [userId]);
        const debtPayments = result.rows;

        const totalMonthlyEMI = debtPayments.reduce((sum, payment) => {
            // Estimate monthly payment frequency
            const monthlyFreq = Math.min(payment.frequency / 6, 1); // Max once per month
            return sum + (parseFloat(payment.avg_amount) * monthlyFreq);
        }, 0);

        return {
            total_monthly_emi: totalMonthlyEMI,
            debt_payments: debtPayments.map(payment => ({
                description: payment.description,
                estimated_monthly: parseFloat(payment.avg_amount),
                frequency: parseInt(payment.frequency),
                merchant: payment.merchant
            })),
            confidence_score: debtPayments.length > 0 ? 0.7 : 0.3
        };
    }

    // Analyze savings patterns
    async analyzeSavings(userId) {
        const incomeQuery = `
            SELECT COALESCE(SUM(amount), 0) as total_income
            FROM transactions 
            WHERE user_id = $1 
            AND type = 'income'
            AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'
        `;

        const expenseQuery = `
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM transactions 
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'
        `;

        const [incomeResult, expenseResult] = await Promise.all([
            pool.query(incomeQuery, [userId]),
            pool.query(expenseQuery, [userId])
        ]);

        const totalIncome = parseFloat(incomeResult.rows[0].total_income);
        const totalExpenses = parseFloat(expenseResult.rows[0].total_expenses);
        const netSavings = totalIncome - totalExpenses;
        const monthlySavings = netSavings / 3; // 3 months average
        const savingsRate = totalIncome > 0 ? (netSavings / totalIncome) : 0;

        return {
            current_rate: savingsRate,
            average_monthly: monthlySavings,
            total_3_months: netSavings,
            confidence_score: totalIncome > 0 ? 0.8 : 0.2
        };
    }

    // Calculate data completeness score
    async calculateDataCompleteness(userId) {
        const queries = {
            total_transactions: `SELECT COUNT(*) FROM transactions WHERE user_id = $1 AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'`,
            income_transactions: `SELECT COUNT(*) FROM transactions WHERE user_id = $1 AND type = 'income' AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'`,
            expense_transactions: `SELECT COUNT(*) FROM transactions WHERE user_id = $1 AND type = 'expense' AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'`,
            categorized_transactions: `SELECT COUNT(*) FROM transactions WHERE user_id = $1 AND category_id IS NOT NULL AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'`,
            unique_months: `SELECT COUNT(DISTINCT DATE_TRUNC('month', transaction_date)) FROM transactions WHERE user_id = $1 AND transaction_date >= CURRENT_DATE - INTERVAL '3 months'`
        };

        const results = {};
        for (const [key, query] of Object.entries(queries)) {
            const result = await pool.query(query, [userId]);
            results[key] = parseInt(result.rows[0].count);
        }

        // Calculate completeness score based on multiple factors
        const factors = {
            transaction_volume: Math.min(results.total_transactions / 50, 1), // 50+ transactions is good
            income_presence: results.income_transactions > 0 ? 1 : 0,
            expense_diversity: Math.min(results.expense_transactions / 30, 1), // 30+ expenses is good
            categorization: results.total_transactions > 0 ? (results.categorized_transactions / results.total_transactions) : 0,
            time_coverage: Math.min(results.unique_months / 3, 1) // 3 months coverage
        };

        const weights = {
            transaction_volume: 0.2,
            income_presence: 0.3,
            expense_diversity: 0.2,
            categorization: 0.15,
            time_coverage: 0.15
        };

        const score = Object.entries(factors).reduce((sum, [key, value]) => {
            return sum + (value * weights[key]);
        }, 0);

        return {
            score: score,
            factors: factors,
            raw_data: results,
            assessment: score >= this.dataCompletenessThresholds.SUFFICIENT ? 'sufficient' : 
                       score >= this.dataCompletenessThresholds.MINIMAL ? 'minimal' : 'insufficient'
        };
    }

    // Generate financial recommendations
    generateRecommendations(income, expenses, debt, savings) {
        const recommendations = [];

        // Income recommendations
        if (income.confidence_score < 0.5) {
            recommendations.push({
                type: 'income',
                priority: 'high',
                message: 'Track your income more consistently for better financial planning'
            });
        }

        // Savings rate recommendations
        if (savings.current_rate < 0.1) {
            recommendations.push({
                type: 'savings',
                priority: 'high',
                message: 'Consider increasing your savings rate to at least 10% of income'
            });
        } else if (savings.current_rate < 0.2) {
            recommendations.push({
                type: 'savings',
                priority: 'medium',
                message: 'Good savings habit! Try to reach 20% savings rate for better financial security'
            });
        }

        // Debt recommendations
        if (debt.total_monthly_emi > 0 && income.estimated_monthly > 0) {
            const debtRatio = debt.total_monthly_emi / income.estimated_monthly;
            if (debtRatio > 0.4) {
                recommendations.push({
                    type: 'debt',
                    priority: 'high',
                    message: 'Your debt-to-income ratio is high. Consider debt consolidation or payment strategies'
                });
            }
        }

        // Expense recommendations
        if (expenses.discretionary > expenses.essential) {
            recommendations.push({
                type: 'expenses',
                priority: 'medium',
                message: 'You spend more on discretionary items than essentials. Review your spending priorities'
            });
        }

        return recommendations;
    }

    // Helper methods
    calculateStandardDeviation(values) {
        const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        const avgSquaredDiff = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
        return Math.sqrt(avgSquaredDiff);
    }

    isGrowingTrend(values) {
        if (values.length < 3) return false;
        let growingCount = 0;
        for (let i = 1; i < values.length; i++) {
            if (values[i] > values[i - 1]) growingCount++;
        }
        return growingCount >= values.length * 0.6;
    }

    isDecliningTrend(values) {
        if (values.length < 3) return false;
        let decliningCount = 0;
        for (let i = 1; i < values.length; i++) {
            if (values[i] < values[i - 1]) decliningCount++;
        }
        return decliningCount >= values.length * 0.6;
    }

    categorizeExpenses(categoryData) {
        const essentialCategories = [
            'food & dining', 'groceries', 'rent', 'utilities', 'bills & utilities',
            'healthcare', 'transportation', 'insurance', 'loan', 'emi'
        ];

        let essential = 0;
        let discretionary = 0;

        categoryData.forEach(cat => {
            const amount = parseFloat(cat.total_amount);
            if (this.isEssentialCategory(cat.category_name)) {
                essential += amount;
            } else {
                discretionary += amount;
            }
        });

        return { essential: essential / 3, discretionary: discretionary / 3 }; // Monthly average
    }

    isEssentialCategory(categoryName) {
        if (!categoryName) return false;
        const essentialKeywords = [
            'food', 'dining', 'grocery', 'rent', 'utility', 'bill', 'healthcare',
            'transport', 'insurance', 'loan', 'emi', 'medical', 'fuel', 'gas'
        ];
        const name = categoryName.toLowerCase();
        return essentialKeywords.some(keyword => name.includes(keyword));
    }
}

module.exports = new FinancialAnalyzer();