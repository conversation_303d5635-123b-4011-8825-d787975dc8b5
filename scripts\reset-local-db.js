const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function resetLocalDatabase() {
    try {
        console.log('🔄 Resetting local database...');
        
        // Stop containers and remove volumes
        console.log('1. Stopping containers and removing data...');
        await execAsync('docker-compose down -v');
        
        // Start containers again
        console.log('2. Starting fresh containers...');
        await execAsync('docker-compose up -d');
        
        // Wait for containers to be ready
        console.log('3. Waiting for databases to be ready...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        console.log('✅ Database reset complete!');
        console.log('\n📝 Next Steps:');
        console.log('1. Run migrations: npm run db:migrate');
        console.log('2. Seed test data: npm run db:seed');
        
    } catch (error) {
        console.error('❌ Failed to reset local database:', error.message);
        process.exit(1);
    }
}

resetLocalDatabase();