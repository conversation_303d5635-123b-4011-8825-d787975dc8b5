const express = require('express');
const { body, param } = require('express-validator');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const goalAI = require('../services/goalAI');
const logger = require('../utils/logger');

const router = express.Router();


// Validation rules for goals
const validateGoal = [
    body('title')
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Title must be between 1 and 200 characters'),
    body('target_amount')
        .isNumeric()
        .custom(value => {
            if (value <= 0) {
                throw new Error('Target amount must be greater than 0');
            }
            return true;
        }),
    body('target_date')
        .isISO8601()
        .withMessage('Target date must be a valid date'),
    body('category')
        .optional()
        .isIn(['car', 'house', 'vacation', 'emergency', 'education', 'debt', 'other'])
        .withMessage('Invalid category'),
    body('priority')
        .optional()
        .isIn(['high', 'medium', 'low'])
        .withMessage('Priority must be high, medium, or low'),
    body('initial_contribution')
        .optional()
        .isNumeric()
        .withMessage('Initial contribution must be a number'),
    handleValidationErrors
];

// Get all goals for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { status = 'active' } = req.query;

        let query = `
            SELECT 
                g.*,
                COALESCE(SUM(gc.amount), 0) as contributed_amount,
                COUNT(gm.id) as total_milestones,
                COUNT(CASE WHEN gm.achieved_at IS NOT NULL THEN 1 END) as completed_milestones
            FROM goals g
            LEFT JOIN goal_contributions gc ON g.id = gc.goal_id
            LEFT JOIN goal_milestones gm ON g.id = gm.goal_id
            WHERE g.user_id = $1
        `;

        const params = [userId];

        if (status !== 'all') {
            query += ' AND g.status = $2';
            params.push(status);
        }

        query += `
            GROUP BY g.id
            ORDER BY
                CASE
                    WHEN g.priority = 'high' THEN 1
                    WHEN g.priority = 'medium' THEN 2
                    WHEN g.priority = 'low' THEN 3
                    ELSE 2
                END ASC,
                g.created_at DESC
        `;

        const result = await pool.query(query, params);
        
        // Enhanced goal data with calculations
        const goals = result.rows.map(goal => {
            const targetAmount = parseFloat(goal.target_amount);
            const totalAmount = parseFloat(goal.contributed_amount) || 0;
            const contributedAmount = totalAmount;
            const remainingAmount = Math.max(targetAmount - totalAmount, 0);
            const progressPercentage = targetAmount > 0 ? (totalAmount / targetAmount) * 100 : 0;
            
            // Calculate days remaining
            const targetDate = new Date(goal.target_date);
            const today = new Date();
            const daysRemaining = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));
            
            // Calculate monthly savings needed
            const monthsRemaining = Math.max(daysRemaining / 30, 1);
            const monthlySavingsNeeded = remainingAmount / monthsRemaining;

            return {
                ...goal,
                target_amount: targetAmount,
                current_amount: totalAmount,
                contributed_amount: contributedAmount,
                remaining_amount: remainingAmount,
                progress_percentage: Math.min(progressPercentage, 100),
                days_remaining: daysRemaining,
                monthly_savings_needed: monthlySavingsNeeded,
                priority: goal.priority,
                priority_display: goal.priority,
                total_milestones: parseInt(goal.total_milestones),
                completed_milestones: parseInt(goal.completed_milestones),
                milestone_progress: goal.total_milestones > 0 ? 
                    (goal.completed_milestones / goal.total_milestones) * 100 : 0,
                status_info: {
                    is_on_track: monthlySavingsNeeded <= (goal.monthly_target || 0) * 1.1,
                    urgency: daysRemaining < 30 ? 'high' : daysRemaining < 90 ? 'medium' : 'low'
                }
            };
        });

        res.json({ goals });
    } catch (error) {
        next(error);
    }
});

// Get single goal with detailed progress
router.get('/:id', authenticateToken, [
    param('id').isUUID().withMessage('Invalid goal ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const goalId = req.params.id;

        // Get goal details
        const goalQuery = `
            SELECT 
                g.*,
                COALESCE(SUM(gc.amount), 0) as contributed_amount
            FROM goals g
            LEFT JOIN goal_contributions gc ON g.id = gc.goal_id
            WHERE g.id = $1 AND g.user_id = $2
            GROUP BY g.id
        `;

        const goalResult = await pool.query(goalQuery, [goalId, userId]);

        if (goalResult.rows.length === 0) {
            return res.status(404).json({ error: 'Goal not found' });
        }

        const goal = goalResult.rows[0];

        // Get milestones
        const milestonesQuery = `
            SELECT * FROM goal_milestones 
            WHERE goal_id = $1 
            ORDER BY target_date ASC
        `;

        const milestonesResult = await pool.query(milestonesQuery, [goalId]);

        // Get recent contributions
        const contributionsQuery = `
            SELECT 
                gc.*,
                t.description,
                t.transaction_date,
                a.name as account_name
            FROM goal_contributions gc
            JOIN transactions t ON gc.transaction_id = t.id
            JOIN accounts a ON t.account_id = a.id
            WHERE gc.goal_id = $1
            ORDER BY gc.created_at DESC
            LIMIT 10
        `;

        const contributionsResult = await pool.query(contributionsQuery, [goalId]);

        // Calculate progress analytics
        const targetAmount = parseFloat(goal.target_amount);
        const currentAmount = parseFloat(goal.current_amount);
        const contributedAmount = parseFloat(goal.contributed_amount);
        const totalAmount = currentAmount + contributedAmount;

        const today = new Date();
        const targetDate = new Date(goal.target_date);
        const daysRemaining = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));
        const monthsRemaining = Math.max(1, daysRemaining / 30);
        const remainingAmount = Math.max(0, targetAmount - totalAmount);

        res.json({
            goal: {
                ...goal,
                target_amount: targetAmount,
                current_amount: totalAmount,
                contributed_amount: contributedAmount,
                remaining_amount: remainingAmount,
                progress_percentage: Math.min((totalAmount / targetAmount) * 100, 100),
                days_remaining: daysRemaining,
                monthly_savings_needed: remainingAmount / monthsRemaining,
                priority: goal.priority,
                priority_display: goal.priority
            },
            milestones: milestonesResult.rows.map(milestone => ({
                ...milestone,
                target_amount: parseFloat(milestone.target_amount),
                is_achieved: !!milestone.achieved_at,
                days_to_target: Math.ceil((new Date(milestone.target_date) - today) / (1000 * 60 * 60 * 24))
            })),
            recent_contributions: contributionsResult.rows.map(contribution => ({
                ...contribution,
                amount: parseFloat(contribution.amount)
            })),
            analytics: {
                average_monthly_contribution: contributedAmount / Math.max(1, 
                    (today - new Date(goal.created_at)) / (1000 * 60 * 60 * 24 * 30)
                ),
                projected_completion_date: new Date(today.getTime() + 
                    (remainingAmount / Math.max(goal.monthly_target || 1000, 1000)) * 30 * 24 * 60 * 60 * 1000
                ),
                on_track: (totalAmount / targetAmount) >= 
                    ((today - new Date(goal.created_at)) / (targetDate - new Date(goal.created_at)))
            }
        });
    } catch (error) {
        next(error);
    }
});

// Create manual goal (Free tier)
router.post('/', authenticateToken, validateGoal, async (req, res, next) => {
    try {
        const userId = req.userId;
        const user = req.user;

        const {
            title,
            description,
            target_amount,
            target_date,
            category = 'other',
            priority = 'medium',
            monthly_target,
            initial_contribution = 0
        } = req.body;


        // Check goal limits for free users
        if (user.subscription_tier === 'free') {
            const countResult = await pool.query(
                'SELECT COUNT(*) FROM goals WHERE user_id = $1 AND status = $2',
                [userId, 'active']
            );
            const activeGoalCount = parseInt(countResult.rows[0].count);
            
            if (activeGoalCount >= 2) {
                return res.status(403).json({
                    error: 'Free users are limited to 2 active goal',
                    current_goals: activeGoalCount,
                    limit: 2,
                    upgrade_url: '/api/user/upgrade-premium'
                });
            }
        }

        // Calculate monthly target if not provided
        const today = new Date();
        const goalDate = new Date(target_date);
        const monthsToGoal = Math.max(1, (goalDate - today) / (1000 * 60 * 60 * 24 * 30));
        const monthlyTarget = target_amount / monthsToGoal;

        // Create goal
        const result = await pool.query(`
            INSERT INTO goals (
                user_id, title, description, target_amount, current_amount, target_date,
                category, priority, monthly_target, creation_method
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        `, [
            userId, title, description, target_amount, initial_contribution, target_date,
            category, priority, monthlyTarget, 'manual'
        ]);

        const goal = result.rows[0];

        // Create basic milestones
        await createBasicMilestones(goal.id, target_amount, monthsToGoal);

        logger.info(`Manual goal created: ${goal.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Goal created successfully',
            goal: {
                ...goal,
                target_amount: parseFloat(goal.target_amount),
                monthly_target: parseFloat(goal.monthly_target),
                priority_display: goal.priority
            }
        });
    } catch (error) {
        next(error);
    }
});

// Update goal
router.put('/:id', authenticateToken, [
    param('id').isUUID().withMessage('Invalid goal ID'),
    ...validateGoal,
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const goalId = req.params.id;
        const {
            title,
            description,
            target_amount,
            target_date,
            category,
            priority,
            status
        } = req.body;

        // Verify goal exists and belongs to user
        const goalCheck = await pool.query(
            'SELECT * FROM goals WHERE id = $1 AND user_id = $2',
            [goalId, userId]
        );

        if (goalCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Goal not found' });
        }

        // Recalculate monthly target if amount or date changed
        const today = new Date();
        const goalDate = new Date(target_date);
        const monthsToGoal = Math.max(1, (goalDate - today) / (1000 * 60 * 60 * 24 * 30));
        const monthlyTarget = target_amount / monthsToGoal;

        // Update goal
        const result = await pool.query(`
            UPDATE goals SET
                title = $3,
                description = $4,
                target_amount = $5,
                target_date = $6,
                category = $7,
                priority = $8,
                monthly_target = $9,
                status = COALESCE($10, status),
                updated_at = NOW()
            WHERE id = $1 AND user_id = $2
            RETURNING *
        `, [
            goalId, userId, title, description, target_amount, target_date,
            category, priority, monthlyTarget, status
        ]);

        const goal = result.rows[0];

        logger.info(`Goal updated: ${goalId} for user: ${userId}`);

        res.json({
            message: 'Goal updated successfully',
            goal: {
                ...goal,
                target_amount: parseFloat(goal.target_amount),
                monthly_target: parseFloat(goal.monthly_target)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Delete goal
router.delete('/:id', authenticateToken, [
    param('id').isUUID().withMessage('Invalid goal ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const goalId = req.params.id;

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Delete related data
            await client.query('DELETE FROM goal_contributions WHERE goal_id = $1', [goalId]);
            await client.query('DELETE FROM goal_milestones WHERE goal_id = $1', [goalId]);
            
            // Delete goal
            const result = await client.query(
                'DELETE FROM goals WHERE id = $1 AND user_id = $2 RETURNING *',
                [goalId, userId]
            );

            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Goal not found' });
            }

            await client.query('COMMIT');

            logger.info(`Goal deleted: ${goalId} for user: ${userId}`);

            res.json({ message: 'Goal deleted successfully' });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    } catch (error) {
        next(error);
    }
});

// Contribute to goal
router.post('/:id/contribute', authenticateToken, [
    param('id').isUUID().withMessage('Invalid goal ID'),
    body('transaction_id').isUUID().withMessage('Transaction ID is required'),
    body('amount').isNumeric().withMessage('Amount must be a number'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const goalId = req.params.id;
        const { transaction_id, amount } = req.body;

        // Verify goal exists and belongs to user
        const goalCheck = await pool.query(
            'SELECT * FROM goals WHERE id = $1 AND user_id = $2',
            [goalId, userId]
        );

        if (goalCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Goal not found' });
        }

        // Verify transaction exists and belongs to user
        const transactionCheck = await pool.query(
            'SELECT * FROM transactions WHERE id = $1 AND user_id = $2',
            [transaction_id, userId]
        );

        if (transactionCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        // Check if contribution already exists
        const existingContribution = await pool.query(
            'SELECT id FROM goal_contributions WHERE goal_id = $1 AND transaction_id = $2',
            [goalId, transaction_id]
        );

        if (existingContribution.rows.length > 0) {
            return res.status(409).json({ error: 'Transaction already contributed to this goal' });
        }

        // Create contribution
        const result = await pool.query(`
            INSERT INTO goal_contributions (goal_id, transaction_id, amount)
            VALUES ($1, $2, $3)
            RETURNING *
        `, [goalId, transaction_id, amount]);

        const contribution = result.rows[0];

        logger.info(`Goal contribution created: ${contribution.id} for goal: ${goalId}`);

        res.status(201).json({
            message: 'Contribution added successfully',
            contribution: {
                ...contribution,
                amount: parseFloat(contribution.amount)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Get goal progress analytics
router.get('/:id/progress', authenticateToken, [
    param('id').isUUID().withMessage('Invalid goal ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const goalId = req.params.id;

        // Get goal with contributions
        const goalQuery = `
            SELECT 
                g.*,
                COALESCE(SUM(gc.amount), 0) as total_contributed
            FROM goals g
            LEFT JOIN goal_contributions gc ON g.id = gc.goal_id
            WHERE g.id = $1 AND g.user_id = $2
            GROUP BY g.id
        `;

        const goalResult = await pool.query(goalQuery, [goalId, userId]);

        if (goalResult.rows.length === 0) {
            return res.status(404).json({ error: 'Goal not found' });
        }

        const goal = goalResult.rows[0];

        // Get monthly contribution history
        const monthlyQuery = `
            SELECT 
                DATE_TRUNC('month', gc.created_at) as month,
                SUM(gc.amount) as monthly_contribution,
                COUNT(gc.id) as contribution_count
            FROM goal_contributions gc
            WHERE gc.goal_id = $1
            GROUP BY DATE_TRUNC('month', gc.created_at)
            ORDER BY month ASC
        `;

        const monthlyResult = await pool.query(monthlyQuery, [goalId]);

        // Calculate analytics
        const targetAmount = parseFloat(goal.target_amount);
        const currentAmount = parseFloat(goal.current_amount);
        const totalContributed = parseFloat(goal.total_contributed);
        const totalAmount = currentAmount + totalContributed;

        const today = new Date();
        const targetDate = new Date(goal.target_date);
        const createdDate = new Date(goal.created_at);
        
        const totalDays = (targetDate - createdDate) / (1000 * 60 * 60 * 24);
        const elapsedDays = (today - createdDate) / (1000 * 60 * 60 * 24);
        const remainingDays = Math.max(0, (targetDate - today) / (1000 * 60 * 60 * 24));

        const expectedProgress = Math.min(elapsedDays / totalDays, 1);
        const actualProgress = totalAmount / targetAmount;

        res.json({
            goal_id: goalId,
            target_amount: targetAmount,
            current_amount: totalAmount,
            progress_percentage: Math.min((totalAmount / targetAmount) * 100, 100),
            days_remaining: Math.ceil(remainingDays),
            monthly_contributions: monthlyResult.rows.map(row => ({
                month: row.month,
                amount: parseFloat(row.monthly_contribution),
                contribution_count: parseInt(row.contribution_count)
            })),
            analytics: {
                expected_progress: expectedProgress * 100,
                actual_progress: actualProgress * 100,
                ahead_behind: (actualProgress - expectedProgress) * 100,
                average_monthly_contribution: totalContributed / Math.max(1, 
                    (today - createdDate) / (1000 * 60 * 60 * 24 * 30)
                ),
                required_monthly_savings: remainingDays > 0 ? 
                    (targetAmount - totalAmount) / (remainingDays / 30) : 0,
                projected_completion: remainingDays > 0 && goal.monthly_target > 0 ?
                    new Date(today.getTime() + 
                        ((targetAmount - totalAmount) / goal.monthly_target) * 30 * 24 * 60 * 60 * 1000
                    ) : null
            }
        });
    } catch (error) {
        next(error);
    }
});

// AI-Powered Goal Setting Routes (Premium)

// Start AI goal session
router.post('/ai/start-session', authenticateToken, requirePremium('ai_goal_setting'), [
    body('goal_statement')
        .trim()
        .isLength({ min: 10, max: 500 })
        .withMessage('Goal statement must be between 10 and 500 characters'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const { goal_statement } = req.body;

        const result = await goalAI.startGoalSession(userId, goal_statement);

        logger.info(`AI goal session started: ${result.sessionId} for user: ${userId}`);

        res.status(201).json({
            message: 'AI goal session started',
            session_id: result.sessionId,
            response: result.response
        });
    } catch (error) {
        next(error);
    }
});

// Continue AI conversation
router.post('/ai/chat', authenticateToken, requirePremium('ai_goal_setting'), [
    body('session_id').isUUID().withMessage('Session ID is required'),
    body('message')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Message must be between 1 and 1000 characters'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const { session_id, message } = req.body;

        const result = await goalAI.continueConversation(session_id, userId, message);

        res.json({
            message: result.message,
            state: result.state,
            next_action: result.next_action,
            data: result
        });
    } catch (error) {
        next(error);
    }
});

// Get AI session status
router.get('/ai/session/:sessionId', authenticateToken, requirePremium('ai_goal_setting'), [
    param('sessionId').isUUID().withMessage('Invalid session ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const sessionId = req.params.sessionId;

        const result = await pool.query(
            'SELECT * FROM goal_ai_sessions WHERE id = $1 AND user_id = $2',
            [sessionId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Session not found' });
        }

        const session = result.rows[0];

        res.json({
            session: {
                id: session.id,
                state: session.conversation_state,
                active: session.session_active,
                created_at: session.created_at,
                updated_at: session.updated_at,
                goal_id: session.goal_id
            },
            conversation: session.conversation_data,
            can_finalize: session.conversation_state === 'completed'
        });
    } catch (error) {
        next(error);
    }
});

// Finalize AI goal creation
router.post('/ai/finalize', authenticateToken, requirePremium('ai_goal_setting'), [
    body('session_id').isUUID().withMessage('Session ID is required'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const { session_id } = req.body;

        const result = await goalAI.finalizeGoal(session_id, userId);

        logger.info(`AI goal finalized: ${result.goal.id} from session: ${session_id}`);

        res.status(201).json({
            message: result.message,
            goal: {
                ...result.goal,
                target_amount: parseFloat(result.goal.target_amount),
                monthly_target: parseFloat(result.goal.monthly_target)
            },
            strategy: result.strategy
        });
    } catch (error) {
        next(error);
    }
});

// Cancel/delete AI session
router.delete('/ai/session/:sessionId', authenticateToken, requirePremium('ai_goal_setting'), [
    param('sessionId').isUUID().withMessage('Invalid session ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const sessionId = req.params.sessionId;

        const result = await pool.query(
            'DELETE FROM goal_ai_sessions WHERE id = $1 AND user_id = $2 RETURNING *',
            [sessionId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Session not found' });
        }

        logger.info(`AI goal session deleted: ${sessionId} for user: ${userId}`);

        res.json({ message: 'AI session cancelled successfully' });
    } catch (error) {
        next(error);
    }
});

// Helper function to create basic milestones
async function createBasicMilestones(goalId, targetAmount, monthsToGoal) {
    const milestoneCount = Math.min(Math.ceil(monthsToGoal / 3), 4); // Every 3 months, max 4 milestones
    
    for (let i = 1; i <= milestoneCount; i++) {
        const percentage = i / milestoneCount;
        const milestoneAmount = targetAmount * percentage;
        const milestoneDate = new Date(Date.now() + (monthsToGoal * percentage) * 30 * 24 * 60 * 60 * 1000);

        await pool.query(`
            INSERT INTO goal_milestones (goal_id, title, target_amount, target_date)
            VALUES ($1, $2, $3, $4)
        `, [
            goalId,
            `${Math.round(percentage * 100)}% Complete`,
            milestoneAmount,
            milestoneDate
        ]);
    }
}

module.exports = router;










