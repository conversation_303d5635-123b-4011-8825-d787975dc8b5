# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=finance-db
DB_USER=postgres
DB_PASSWORD=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration - CRITICAL FOR AUTHENTICATION
JWT_SECRET=your-super-secret-jwt-key-here-minimum-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-different-from-jwt-secret

# API Keys
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
COHERE_API_KEY=your-cohere-api-key-here

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=finance-manager-uploads

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging
LOG_LEVEL=info