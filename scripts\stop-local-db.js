const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function stopLocalDatabase() {
    try {
        console.log('🛑 Stopping local database containers...');
        
        const { stdout, stderr } = await execAsync('docker-compose down');
        
        if (stderr) {
            console.error('Docker Compose Error:', stderr);
        }
        
        console.log('✅ Local database containers stopped');
        console.log('\n📊 Final Status:');
        console.log(stdout);
        
        console.log('\n💡 To remove all data and start fresh:');
        console.log('docker-compose down -v');
        
    } catch (error) {
        console.error('❌ Failed to stop local database:', error.message);
        process.exit(1);
    }
}

stopLocalDatabase();