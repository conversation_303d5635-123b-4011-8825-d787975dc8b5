const { pool } = require('../config/database');
const logger = require('../utils/logger');

class AnalyticsService {
    async getSpendingTrends(userId, months = 6) {
        try {
            const query = `
                SELECT 
                    DATE_TRUNC('month', transaction_date) as month,
                    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expenses,
                    SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
                    COUNT(*) as transaction_count
                FROM transactions 
                WHERE user_id = $1 
                AND transaction_date >= CURRENT_DATE - INTERVAL '${months} months'
                GROUP BY DATE_TRUNC('month', transaction_date)
                ORDER BY month DESC
            `;

            const result = await pool.query(query, [userId]);
            return result.rows;
        } catch (error) {
            logger.error('Failed to get spending trends:', error);
            throw error;
        }
    }

    async getCategoryBreakdown(userId, startDate, endDate) {
        try {
            const query = `
                SELECT 
                    c.name as category_name,
                    c.color,
                    SUM(t.amount) as total_amount,
                    COUNT(t.id) as transaction_count,
                    AVG(t.amount) as avg_amount
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = $1 
                AND t.transaction_date BETWEEN $2 AND $3
                AND t.type = 'expense'
                GROUP BY c.id, c.name, c.color
                ORDER BY total_amount DESC
            `;

            const result = await pool.query(query, [userId, startDate, endDate]);
            return result.rows;
        } catch (error) {
            logger.error('Failed to get category breakdown:', error);
            throw error;
        }
    }

    async getPredictiveAlerts(userId) {
        try {
            // Get current month spending
            const currentMonth = new Date();
            const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
            const currentSpending = await this.getCurrentMonthSpending(userId, startOfMonth);

            // Get historical average
            const historicalData = await this.getHistoricalSpending(userId, 6);
            const avgMonthlySpending = this.calculateAverage(historicalData);

            // Project spending for the month
            const daysInMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate();
            const currentDay = currentMonth.getDate();
            const projectedSpending = (currentSpending / currentDay) * daysInMonth;

            const alerts = [];

            // Overspending alert
            if (projectedSpending > avgMonthlySpending * 1.2) {
                alerts.push({
                    type: 'overspending_alert',
                    message: `You're on track to overspend by $${(projectedSpending - avgMonthlySpending).toFixed(2)} this month`,
                    severity: 'high',
                    projected_amount: projectedSpending,
                    average_amount: avgMonthlySpending
                });
            }

            // Category-specific alerts
            const categoryAlerts = await this.generateCategoryAlerts(userId);
            alerts.push(...categoryAlerts);

            return alerts;
        } catch (error) {
            logger.error('Failed to generate predictive alerts:', error);
            throw error;
        }
    }

    async getCurrentMonthSpending(userId, startDate) {
        const query = `
            SELECT COALESCE(SUM(amount), 0) as total
            FROM transactions
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= $2
            AND transaction_date < CURRENT_DATE + INTERVAL '1 day'
        `;

        const result = await pool.query(query, [userId, startDate]);
        return parseFloat(result.rows[0].total);
    }

    async getHistoricalSpending(userId, months) {
        const query = `
            SELECT 
                DATE_TRUNC('month', transaction_date) as month,
                SUM(amount) as total
            FROM transactions
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= CURRENT_DATE - INTERVAL '${months} months'
            GROUP BY DATE_TRUNC('month', transaction_date)
            ORDER BY month
        `;

        const result = await pool.query(query, [userId]);
        return result.rows.map(row => parseFloat(row.total));
    }

    calculateAverage(values) {
        if (values.length === 0) return 0;
        return values.reduce((sum, value) => sum + value, 0) / values.length;
    }

    async generateCategoryAlerts(userId) {
        const query = `
            SELECT 
                b.id,
                b.amount as budget_amount,
                b.alert_threshold,
                c.name as category_name,
                COALESCE(SUM(t.amount), 0) as spent_amount
            FROM budgets b
            JOIN categories c ON b.category_id = c.id
            LEFT JOIN transactions t ON t.category_id = c.id 
                AND t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date BETWEEN b.start_date AND b.end_date
            WHERE b.user_id = $1 
            AND b.is_active = true
            AND CURRENT_DATE BETWEEN b.start_date AND b.end_date
            GROUP BY b.id, b.amount, b.alert_threshold, c.name
        `;

        const result = await pool.query(query, [userId]);
        const alerts = [];

        result.rows.forEach(row => {
            const spentPercentage = row.spent_amount / row.budget_amount;
            
            if (spentPercentage >= row.alert_threshold) {
                alerts.push({
                    type: 'budget_alert',
                    category: row.category_name,
                    message: `You've spent ${(spentPercentage * 100).toFixed(1)}% of your ${row.category_name} budget`,
                    severity: spentPercentage >= 0.9 ? 'high' : 'medium',
                    spent_amount: row.spent_amount,
                    budget_amount: row.budget_amount,
                    percentage: spentPercentage
                });
            }
        });

        return alerts;
    }

    async getAccountBalanceHistory(userId, accountId, days = 30) {
        try {
            const query = `
                WITH RECURSIVE date_series AS (
                    SELECT CURRENT_DATE - INTERVAL '${days} days' as date
                    UNION ALL
                    SELECT date + INTERVAL '1 day'
                    FROM date_series
                    WHERE date < CURRENT_DATE
                ),
                daily_balances AS (
                    SELECT 
                        ds.date,
                        a.balance - COALESCE(SUM(
                            CASE 
                                WHEN t.transaction_date > ds.date AND t.type = 'expense' THEN -t.amount
                                WHEN t.transaction_date > ds.date AND t.type = 'income' THEN t.amount
                                ELSE 0
                            END
                        ), 0) as balance
                    FROM date_series ds
                    CROSS JOIN accounts a
                    LEFT JOIN transactions t ON t.account_id = a.id
                    WHERE a.id = $2 AND a.user_id = $1
                    GROUP BY ds.date, a.balance
                    ORDER BY ds.date
                )
                SELECT date, balance FROM daily_balances
            `;

            const result = await pool.query(query, [userId, accountId]);
            return result.rows;
        } catch (error) {
            logger.error('Failed to get account balance history:', error);
            throw error;
        }
    }
}

module.exports = new AnalyticsService();