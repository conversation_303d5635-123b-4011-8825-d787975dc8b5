const { CohereClientV2 } = require('cohere-ai');
const crypto = require('crypto');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

class AICategorizer {
    constructor() {
        this.cohere = new CohereClientV2({
            token: process.env.COHERE_API_KEY
        });
        this.batchSize = 75; // Optimal batch size for token efficiency
        this.maxDescriptionLength = 50;
    }

    // Main categorization method
    async categorizeTransactions(transactions, userCategories = []) {
        try {
            logger.info(`Starting AI categorization for ${transactions.length} transactions`);
            
            // Check cache first
            const cachedResults = await this.checkCache(transactions);
            const uncachedTransactions = transactions.filter((_, index) => !cachedResults[index]);
            
            if (uncachedTransactions.length === 0) {
                logger.info('All transactions found in cache');
                return cachedResults;
            }

            // Process uncached transactions in batches
            const chunks = this.chunkArray(uncachedTransactions, this.batchSize);
            const aiResults = [];
            
            for (let i = 0; i < chunks.length; i++) {
                const chunk = chunks[i];
                logger.info(`Processing batch ${i + 1}/${chunks.length} (${chunk.length} transactions)`);
                
                try {
                    const batchResult = await this.processBatch(chunk, userCategories);
                    aiResults.push(...batchResult);
                    
                    // Cache results
                    await this.cacheResults(chunk, batchResult);
                    
                    // Rate limiting - small delay between batches
                    if (i < chunks.length - 1) {
                        await this.delay(1000);
                    }
                } catch (error) {
                    logger.error(`Batch ${i + 1} failed:`, error);
                    // Add fallback categorization for failed batch
                    const fallbackResults = chunk.map(() => ({
                        category: 'Uncategorized',
                        confidence: 0.1,
                        merchant: null
                    }));
                    aiResults.push(...fallbackResults);
                }
            }

            // Merge cached and AI results
            const finalResults = [];
            let aiIndex = 0;
            
            for (let i = 0; i < transactions.length; i++) {
                if (cachedResults[i]) {
                    finalResults.push(cachedResults[i]);
                } else {
                    finalResults.push(aiResults[aiIndex++]);
                }
            }

            logger.info(`AI categorization completed. Cache hits: ${cachedResults.filter(r => r).length}, AI processed: ${aiResults.length}`);
            return finalResults;
            
        } catch (error) {
            logger.error('AI categorization failed:', error);
            throw error;
        }
    }

    // Process a batch of transactions with AI
    async processBatch(transactions, userCategories) {
        const essentials = transactions.map(tx => ({
            desc: this.cleanDescription(tx.description),
            amt: Math.abs(parseFloat(tx.amount)),
            type: parseFloat(tx.amount) >= 0 ? 'credit' : 'debit'
        }));

        const prompt = this.buildPrompt(essentials, userCategories);
        
        try {
            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a financial transaction categorization expert. Analyze transaction descriptions and categorize them accurately. Return only valid JSON.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2000,
                stream: false
            });

            const content = response.message.content[0].text.trim();
            const results = JSON.parse(content);
            
            // Validate results
            if (!Array.isArray(results) || results.length !== transactions.length) {
                throw new Error('Invalid AI response format');
            }

            return results.map(result => ({
                category: result.category || 'Uncategorized',
                confidence: Math.min(Math.max(result.confidence || 0.5, 0), 1),
                merchant: result.merchant || null
            }));

        } catch (error) {
            logger.error('Cohere API call failed:', error);
            throw error;
        }
    }

    // Clean and optimize transaction descriptions for AI
    cleanDescription(description) {
        if (!description) return '';
        
        return description
            .replace(/\d{4}\*+\d{4}/g, '') // Remove card numbers
            .replace(/\b\d{2}\/\d{2}\/\d{4}\b/g, '') // Remove dates
            .replace(/\b\d{4}-\d{2}-\d{2}\b/g, '') // Remove ISO dates
            .replace(/REF#?\s*\w+/gi, '') // Remove reference numbers
            .replace(/TXN#?\s*\w+/gi, '') // Remove transaction numbers
            .replace(/AUTH#?\s*\w+/gi, '') // Remove auth codes
            .replace(/\b\d{10,}\b/g, '') // Remove long numbers
            .replace(/\s+/g, ' ') // Normalize whitespace
            .substring(0, this.maxDescriptionLength)
            .trim();
    }

    // Build optimized prompt for AI
    buildPrompt(transactions, userCategories) {
        const categoryList = userCategories.length > 0 
            ? userCategories.map(c => c.name).join(', ')
            : 'Food & Dining, Transportation, Shopping, Entertainment, Bills & Utilities, Healthcare, Income, Transfer, Uncategorized';

        return `Categorize these ${transactions.length} transactions. Use these categories: ${categoryList}

Return JSON array with exactly ${transactions.length} objects:
[{"category":"CategoryName","confidence":0.95,"merchant":"MerchantName"}]

Transactions:
${transactions.map((tx, i) => `${i + 1}. ${tx.desc} $${tx.amt} (${tx.type})`).join('\n')}

Rules:
- confidence: 0.1-1.0 (higher for obvious matches)
- merchant: extract clean business name or null
- Use exact category names from the list
- Default to "Uncategorized" if unsure`;
    }

    // Check cache for existing categorizations
    async checkCache(transactions) {
        const results = new Array(transactions.length).fill(null);
        
        try {
            for (let i = 0; i < transactions.length; i++) {
                const tx = transactions[i];
                const cleanedDesc = this.cleanDescription(tx.description);
                const hash = this.generateHash(cleanedDesc);
                
                const cacheResult = await pool.query(
                    'SELECT suggested_category, confidence_score, merchant FROM ai_categorization_cache WHERE description_hash = $1',
                    [hash]
                );
                
                if (cacheResult.rows.length > 0) {
                    const cached = cacheResult.rows[0];
                    results[i] = {
                        category: cached.suggested_category,
                        confidence: parseFloat(cached.confidence_score),
                        merchant: cached.merchant
                    };
                    
                    // Update usage stats
                    await pool.query(
                        'UPDATE ai_categorization_cache SET last_used = NOW(), usage_count = usage_count + 1 WHERE description_hash = $1',
                        [hash]
                    );
                }
            }
        } catch (error) {
            logger.error('Cache check failed:', error);
        }
        
        return results;
    }

    // Cache AI results for future use
    async cacheResults(transactions, results) {
        try {
            for (let i = 0; i < transactions.length; i++) {
                const tx = transactions[i];
                const result = results[i];
                const cleanedDesc = this.cleanDescription(tx.description);
                const hash = this.generateHash(cleanedDesc);
                
                await pool.query(`
                    INSERT INTO ai_categorization_cache 
                    (description_hash, original_description, cleaned_description, suggested_category, confidence_score, merchant)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (description_hash) 
                    DO UPDATE SET 
                        last_used = NOW(),
                        usage_count = ai_categorization_cache.usage_count + 1
                `, [
                    hash,
                    tx.description,
                    cleanedDesc,
                    result.category,
                    result.confidence,
                    result.merchant
                ]);
            }
            
            logger.info(`Cached ${results.length} categorization results`);
        } catch (error) {
            logger.error('Cache storage failed:', error);
        }
    }

    // Generate hash for description
    generateHash(description) {
        return crypto.createHash('sha256').update(description.toLowerCase()).digest('hex');
    }

    // Split array into chunks
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    // Delay utility
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Clean up old cache entries (run periodically)
    async cleanupCache() {
        try {
            // Remove entries older than 6 months with low usage
            await pool.query(`
                DELETE FROM ai_categorization_cache 
                WHERE created_at < NOW() - INTERVAL '6 months' 
                AND usage_count < 3
            `);
            
            logger.info('AI cache cleanup completed');
        } catch (error) {
            logger.error('Cache cleanup failed:', error);
        }
    }

    // Test Cohere connection
    async testConnection() {
        try {
            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'user',
                        content: 'Test connection. Reply with "OK".'
                    }
                ],
                temperature: 0.1,
                max_tokens: 10,
                stream: false
            });

            const content = response.message.content[0].text.trim();
            logger.info(`Cohere connection test successful: ${content}`);
            return true;
        } catch (error) {
            logger.error('Cohere connection test failed:', error);
            return false;
        }
    }
}

module.exports = new AICategorizer();