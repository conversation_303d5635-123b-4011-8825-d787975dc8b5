const { pool } = require('../config/database');
const financialAnalyzer = require('./financialAnalyzer');
const { CohereClientV2 } = require('cohere-ai');
const logger = require('../utils/logger');

class RecommendationEngine {
    constructor() {
        this.cohere = new CohereClientV2({
            token: process.env.COHERE_API_KEY
        });

        this.recommendationTypes = {
            GOAL_ACCELERATION: {
                triggers: ['slow_goal_progress', 'high_discretionary_spending'],
                template: "Reduce {category} spending by {percentage}% to free up ₹{amount} monthly for your {goal_name}",
                priority: 5,
                frequency: 'weekly'
            },
            BUDGET_OPTIMIZATION: {
                triggers: ['category_overspend', 'unused_budget'],
                template: "You've overspent on {category} by ₹{amount}. Consider reallocating from {underused_category}",
                priority: 4,
                frequency: 'daily'
            },
            BEHAVIORAL_INSIGHT: {
                triggers: ['spending_pattern_change', 'seasonal_variation'],
                template: "Your {category} spending increased by {percentage}% this month. Here's how to optimize it",
                priority: 3,
                frequency: 'weekly'
            },
            PROACTIVE_ALERT: {
                triggers: ['unusual_transaction', 'cash_flow_risk'],
                template: "Unusual {category} spending detected. This might impact your {goal_name} timeline",
                priority: 5,
                frequency: 'immediate'
            },
            OPPORTUNITY_ALERT: {
                triggers: ['subscription_optimization', 'cashback_opportunity'],
                template: "You could save ₹{amount} monthly by optimizing your {service_type} subscriptions",
                priority: 2,
                frequency: 'monthly'
            }
        };
    }

    // Main recommendation generation method
    async generateRecommendations(userId) {
        try {
            logger.info(`Generating recommendations for user: ${userId}`);

            // Check user subscription for limits
            const userResult = await pool.query(
                'SELECT subscription_tier FROM users WHERE id = $1',
                [userId]
            );

            if (userResult.rows.length === 0) {
                throw new Error('User not found');
            }

            const user = userResult.rows[0];
            const isFreeTier = user.subscription_tier === 'free';

            // Get existing active recommendations count
            const existingCount = await pool.query(
                'SELECT COUNT(*) FROM ai_recommendations WHERE user_id = $1 AND status = $2 AND created_at >= CURRENT_DATE - INTERVAL \'7 days\'',
                [userId, 'active']
            );

            const weeklyRecommendations = parseInt(existingCount.rows[0].count);

            // Apply free tier limits (3 recommendations per week)
            if (isFreeTier && weeklyRecommendations >= 3) {
                logger.info(`Free tier user ${userId} has reached weekly recommendation limit`);
                return {
                    recommendations: [],
                    message: 'You have reached your weekly recommendation limit. Upgrade to Premium for unlimited recommendations.',
                    limit_reached: true
                };
            }

            // Build financial profile
            const financialProfile = await financialAnalyzer.buildFinancialProfile(userId);

            // Analyze different aspects
            const [spendingAnalysis, goalAnalysis, budgetAnalysis] = await Promise.all([
                this.analyzeSpendingPatterns(userId),
                this.analyzeGoalProgress(userId),
                this.analyzeBudgetPerformance(userId)
            ]);

            // Generate AI recommendations
            const aiRecommendations = await this.generateAIRecommendations(
                userId,
                financialProfile,
                { spendingAnalysis, goalAnalysis, budgetAnalysis }
            );

            // Store recommendations in database
            const storedRecommendations = await this.storeRecommendations(userId, aiRecommendations);

            logger.info(`Generated ${storedRecommendations.length} recommendations for user: ${userId}`);

            return {
                recommendations: storedRecommendations,
                financial_profile: financialProfile,
                limit_reached: false
            };

        } catch (error) {
            logger.error('Failed to generate recommendations:', error);
            throw error;
        }
    }

    // Analyze spending patterns
    async analyzeSpendingPatterns(userId, timeframe = 90) {
        try {
            // Get category spending trends
            const categoryTrendsQuery = `
                SELECT 
                    c.name as category_name,
                    DATE_TRUNC('month', t.transaction_date) as month,
                    SUM(t.amount) as monthly_amount,
                    COUNT(t.id) as transaction_count
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date >= CURRENT_DATE - INTERVAL '${timeframe} days'
                GROUP BY c.name, DATE_TRUNC('month', t.transaction_date)
                ORDER BY month DESC, monthly_amount DESC
            `;

            const categoryTrends = await pool.query(categoryTrendsQuery, [userId]);

            // Identify recurring patterns
            const recurringQuery = `
                SELECT 
                    merchant,
                    description,
                    AVG(amount) as avg_amount,
                    COUNT(*) as frequency,
                    EXTRACT(DOW FROM transaction_date) as day_of_week
                FROM transactions
                WHERE user_id = $1 
                AND type = 'expense'
                AND transaction_date >= CURRENT_DATE - INTERVAL '${timeframe} days'
                AND merchant IS NOT NULL
                GROUP BY merchant, description, EXTRACT(DOW FROM transaction_date)
                HAVING COUNT(*) >= 3
                ORDER BY frequency DESC, avg_amount DESC
            `;

            const recurringPatterns = await pool.query(recurringQuery, [userId]);

            // Detect anomalies (transactions significantly higher than average)
            const anomalyQuery = `
                WITH category_averages AS (
                    SELECT 
                        category_id,
                        AVG(amount) as avg_amount,
                        STDDEV(amount) as std_amount
                    FROM transactions
                    WHERE user_id = $1 
                    AND type = 'expense'
                    AND transaction_date >= CURRENT_DATE - INTERVAL '${timeframe} days'
                    GROUP BY category_id
                )
                SELECT 
                    t.*,
                    c.name as category_name,
                    ca.avg_amount,
                    (t.amount - ca.avg_amount) / NULLIF(ca.std_amount, 0) as z_score
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                LEFT JOIN category_averages ca ON t.category_id = ca.category_id
                WHERE t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date >= CURRENT_DATE - INTERVAL '30 days'
                AND ca.std_amount > 0
                AND ABS((t.amount - ca.avg_amount) / ca.std_amount) > 2
                ORDER BY ABS((t.amount - ca.avg_amount) / ca.std_amount) DESC
                LIMIT 10
            `;

            const unusualTransactions = await pool.query(anomalyQuery, [userId]);

            // Find optimization opportunities
            const optimizationQuery = `
                SELECT 
                    c.name as category_name,
                    SUM(t.amount) as total_amount,
                    COUNT(t.id) as transaction_count,
                    AVG(t.amount) as avg_amount,
                    MAX(t.amount) as max_amount
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY c.name
                HAVING SUM(t.amount) > 1000 -- Focus on categories with significant spending
                ORDER BY SUM(t.amount) DESC
                LIMIT 10
            `;

            const optimizationOpportunities = await pool.query(optimizationQuery, [userId]);

            return {
                category_trends: categoryTrends.rows,
                recurring_patterns: recurringPatterns.rows,
                unusual_transactions: unusualTransactions.rows,
                optimization_opportunities: optimizationOpportunities.rows
            };

        } catch (error) {
            logger.error('Failed to analyze spending patterns:', error);
            throw error;
        }
    }

    // Analyze goal progress
    async analyzeGoalProgress(userId) {
        try {
            const goalsQuery = `
                SELECT 
                    g.*,
                    COALESCE(SUM(gc.amount), 0) as contributed_amount,
                    (g.target_amount - g.current_amount - COALESCE(SUM(gc.amount), 0)) as remaining_amount
                FROM goals g
                LEFT JOIN goal_contributions gc ON g.id = gc.goal_id
                WHERE g.user_id = $1 AND g.status = 'active'
                GROUP BY g.id
            `;

            const goalsResult = await pool.query(goalsQuery, [userId]);
            const goals = goalsResult.rows;

            const goalAnalysis = goals.map(goal => {
                const targetAmount = parseFloat(goal.target_amount);
                const currentAmount = parseFloat(goal.current_amount);
                const contributedAmount = parseFloat(goal.contributed_amount);
                const totalAmount = currentAmount + contributedAmount;
                const remainingAmount = parseFloat(goal.remaining_amount);

                const today = new Date();
                const targetDate = new Date(goal.target_date);
                const createdDate = new Date(goal.created_at);
                
                const totalDays = (targetDate - createdDate) / (1000 * 60 * 60 * 24);
                const elapsedDays = (today - createdDate) / (1000 * 60 * 60 * 24);
                const remainingDays = Math.max(0, (targetDate - today) / (1000 * 60 * 60 * 24));

                const expectedProgress = Math.min(elapsedDays / totalDays, 1);
                const actualProgress = totalAmount / targetAmount;
                const progressGap = actualProgress - expectedProgress;

                const monthlyTarget = parseFloat(goal.monthly_target) || 0;
                const requiredMonthlySavings = remainingDays > 0 ? remainingAmount / (remainingDays / 30) : 0;

                return {
                    goal_id: goal.id,
                    title: goal.title,
                    target_amount: targetAmount,
                    current_progress: actualProgress,
                    expected_progress: expectedProgress,
                    progress_gap: progressGap,
                    pace_analysis: progressGap < -0.1 ? 'behind' : progressGap > 0.1 ? 'ahead' : 'on_track',
                    monthly_target: monthlyTarget,
                    required_monthly_savings: requiredMonthlySavings,
                    bottlenecks: this.identifyGoalBottlenecks(goal, progressGap, requiredMonthlySavings, monthlyTarget),
                    acceleration_opportunities: this.findAccelerationOptions(goal, requiredMonthlySavings, monthlyTarget)
                };
            });

            return goalAnalysis;

        } catch (error) {
            logger.error('Failed to analyze goal progress:', error);
            throw error;
        }
    }

    // Analyze budget performance
    async analyzeBudgetPerformance(userId) {
        try {
            const budgetQuery = `
                SELECT 
                    b.*,
                    c.name as category_name,
                    COALESCE(SUM(t.amount), 0) as spent_amount
                FROM budgets b
                LEFT JOIN categories c ON b.category_id = c.id
                LEFT JOIN transactions t ON t.category_id = c.id 
                    AND t.user_id = $1 
                    AND t.type = 'expense'
                    AND t.transaction_date BETWEEN b.start_date AND b.end_date
                WHERE b.user_id = $1 AND b.is_active = true
                GROUP BY b.id, c.name
            `;

            const budgetResult = await pool.query(budgetQuery, [userId]);
            const budgets = budgetResult.rows;

            const overspentCategories = [];
            const underutilizedCategories = [];
            const reallocationSuggestions = [];

            budgets.forEach(budget => {
                const budgetAmount = parseFloat(budget.amount);
                const spentAmount = parseFloat(budget.spent_amount);
                const utilizationRate = spentAmount / budgetAmount;

                if (utilizationRate > 1) {
                    overspentCategories.push({
                        category: budget.category_name,
                        budget_amount: budgetAmount,
                        spent_amount: spentAmount,
                        overspend_amount: spentAmount - budgetAmount,
                        overspend_percentage: ((spentAmount - budgetAmount) / budgetAmount) * 100
                    });
                } else if (utilizationRate < 0.5) {
                    underutilizedCategories.push({
                        category: budget.category_name,
                        budget_amount: budgetAmount,
                        spent_amount: spentAmount,
                        unused_amount: budgetAmount - spentAmount,
                        utilization_rate: utilizationRate * 100
                    });
                }
            });

            // Generate reallocation suggestions
            if (overspentCategories.length > 0 && underutilizedCategories.length > 0) {
                overspentCategories.forEach(overspent => {
                    const bestMatch = underutilizedCategories.find(underused => 
                        underused.unused_amount >= overspent.overspend_amount
                    );
                    
                    if (bestMatch) {
                        reallocationSuggestions.push({
                            from_category: bestMatch.category,
                            to_category: overspent.category,
                            suggested_amount: Math.min(overspent.overspend_amount, bestMatch.unused_amount),
                            impact: `Reallocate ₹${Math.min(overspent.overspend_amount, bestMatch.unused_amount)} from ${bestMatch.category} to ${overspent.category}`
                        });
                    }
                });
            }

            const budgetEfficiency = budgets.length > 0 ? 
                budgets.reduce((sum, budget) => {
                    const utilization = Math.min(parseFloat(budget.spent_amount) / parseFloat(budget.amount), 1);
                    return sum + utilization;
                }, 0) / budgets.length : 0;

            return {
                overspent_categories: overspentCategories,
                underutilized_categories: underutilizedCategories,
                budget_efficiency: budgetEfficiency,
                reallocation_suggestions: reallocationSuggestions
            };

        } catch (error) {
            logger.error('Failed to analyze budget performance:', error);
            throw error;
        }
    }

    // Generate AI-powered recommendations
    async generateAIRecommendations(userId, financialProfile, analysisData) {
        try {
            const prompt = this.buildRecommendationPrompt(userId, financialProfile, analysisData);

            const response = await this.cohere.chat({
                model: 'command-r-plus',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert financial advisor providing personalized recommendations to Indian users. Focus on actionable, specific advice with clear potential savings amounts.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 1000
            });

            const content = response.message.content[0].text.trim();
            const recommendations = JSON.parse(content);

            // Validate and enhance recommendations
            return recommendations.map(rec => ({
                ...rec,
                potential_savings: parseFloat(rec.potential_savings) || 0,
                confidence_score: Math.min(Math.max(parseFloat(rec.confidence_score) || 0.5, 0), 1),
                priority: this.convertPriorityToNumber(rec.priority),
                expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
            }));

        } catch (error) {
            logger.error('Failed to generate AI recommendations:', error);
            // Return fallback recommendations
            return this.generateFallbackRecommendations(analysisData);
        }
    }

    // Build comprehensive prompt for AI
    buildRecommendationPrompt(userId, financialProfile, analysisData) {
        const monthlyIncome = financialProfile.income?.estimated_monthly || 0;
        const monthlyExpenses = financialProfile.expenses?.total_monthly || 0;
        const savingsRate = financialProfile.savings?.current_rate || 0;

        return `
Analyze this user's financial data and generate 3-5 personalized recommendations:

Financial Profile:
- Monthly Income: ₹${monthlyIncome.toLocaleString('en-IN')}
- Monthly Expenses: ₹${monthlyExpenses.toLocaleString('en-IN')}
- Savings Rate: ${(savingsRate * 100).toFixed(1)}%
- Data Completeness: ${(financialProfile.data_completeness?.score * 100 || 50).toFixed(1)}%

Spending Analysis:
- Top spending categories: ${analysisData.spendingAnalysis.optimization_opportunities.slice(0, 3).map(cat => `${cat.category_name}: ₹${cat.total_amount}`).join(', ')}
- Unusual transactions: ${analysisData.spendingAnalysis.unusual_transactions.length} detected

Goal Analysis:
- Active goals: ${analysisData.goalAnalysis.length}
- Goals behind schedule: ${analysisData.goalAnalysis.filter(g => g.pace_analysis === 'behind').length}

Budget Analysis:
- Overspent categories: ${analysisData.budgetAnalysis.overspent_categories.length}
- Underutilized budgets: ${analysisData.budgetAnalysis.underutilized_categories.length}
- Budget efficiency: ${(analysisData.budgetAnalysis.budget_efficiency * 100).toFixed(1)}%

Generate recommendations as JSON array:
[
  {
    "type": "goal_focused|budget_optimization|behavioral_insight|proactive_alert|opportunity_alert",
    "category": "spending_reduction|income_boost|budget_reallocation|subscription_optimization",
    "title": "Short actionable title (max 50 chars)",
    "description": "Detailed explanation with specific data (max 200 chars)",
    "action_text": "Clear action step (max 100 chars)",
    "potential_savings": number, // Monthly savings in INR
    "confidence_score": 0.1-1.0,
    "priority": 1-5, // 5 = highest priority
    "goal_id": null // Only if related to specific goal
  }
]

Focus on:
1. Specific, actionable advice tied to actual data
2. Clear potential savings amounts in INR
3. Indian financial context and spending patterns
4. Realistic and achievable recommendations
5. Prioritize high-impact, easy-to-implement changes

Limit to 5 recommendations maximum.
        `;
    }

    // Generate fallback recommendations when AI fails
    generateFallbackRecommendations(analysisData) {
        const recommendations = [];

        // Budget overspend recommendation
        if (analysisData.budgetAnalysis.overspent_categories.length > 0) {
            const topOverspend = analysisData.budgetAnalysis.overspent_categories[0];
            recommendations.push({
                type: 'budget_optimization',
                category: 'spending_reduction',
                title: `Reduce ${topOverspend.category} spending`,
                description: `You've overspent on ${topOverspend.category} by ₹${topOverspend.overspend_amount.toFixed(0)}. Consider setting stricter limits.`,
                action_text: `Reduce ${topOverspend.category} by 20%`,
                potential_savings: topOverspend.overspend_amount * 0.2,
                confidence_score: 0.8,
                priority: 4,
                goal_id: null
            });
        }

        // High spending category recommendation
        if (analysisData.spendingAnalysis.optimization_opportunities.length > 0) {
            const topSpending = analysisData.spendingAnalysis.optimization_opportunities[0];
            recommendations.push({
                type: 'behavioral_insight',
                category: 'spending_reduction',
                title: `Optimize ${topSpending.category_name} expenses`,
                description: `You spent ₹${topSpending.total_amount.toFixed(0)} on ${topSpending.category_name} this month. Small reductions can add up.`,
                action_text: `Reduce ${topSpending.category_name} by 15%`,
                potential_savings: topSpending.total_amount * 0.15,
                confidence_score: 0.7,
                priority: 3,
                goal_id: null
            });
        }

        // Goal progress recommendation
        const behindGoals = analysisData.goalAnalysis.filter(g => g.pace_analysis === 'behind');
        if (behindGoals.length > 0) {
            const goal = behindGoals[0];
            recommendations.push({
                type: 'goal_focused',
                category: 'goal_acceleration',
                title: `Accelerate ${goal.title} savings`,
                description: `Your ${goal.title} is behind schedule. Increase monthly savings to stay on track.`,
                action_text: `Save ₹${(goal.required_monthly_savings - goal.monthly_target).toFixed(0)} more monthly`,
                potential_savings: 0,
                confidence_score: 0.9,
                priority: 5,
                goal_id: goal.goal_id
            });
        }

        return recommendations.slice(0, 3); // Limit to 3 fallback recommendations
    }

    // Store recommendations in database
    async storeRecommendations(userId, recommendations) {
        try {
            const storedRecommendations = [];

            for (const rec of recommendations) {
                const result = await pool.query(`
                    INSERT INTO ai_recommendations (
                        user_id, type, category, title, description, action_text,
                        potential_savings, confidence_score, priority, data_context,
                        goal_id, expires_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    RETURNING *
                `, [
                    userId,
                    rec.type,
                    rec.category,
                    rec.title,
                    rec.description,
                    rec.action_text,
                    rec.potential_savings,
                    rec.confidence_score,
                    rec.priority,
                    JSON.stringify(rec.data_context || {}),
                    rec.goal_id,
                    rec.expires_at
                ]);

                storedRecommendations.push(result.rows[0]);
            }

            return storedRecommendations;

        } catch (error) {
            logger.error('Failed to store recommendations:', error);
            throw error;
        }
    }

    // Helper methods
    identifyGoalBottlenecks(goal, progressGap, requiredMonthlySavings, monthlyTarget) {
        const bottlenecks = [];

        if (progressGap < -0.2) {
            bottlenecks.push('significantly_behind_schedule');
        }

        if (requiredMonthlySavings > monthlyTarget * 1.5) {
            bottlenecks.push('insufficient_monthly_savings');
        }

        if (new Date(goal.target_date) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) {
            bottlenecks.push('approaching_deadline');
        }

        return bottlenecks;
    }

    findAccelerationOptions(goal, requiredMonthlySavings, monthlyTarget) {
        const options = [];

        if (requiredMonthlySavings > monthlyTarget) {
            const additionalSavings = requiredMonthlySavings - monthlyTarget;
            options.push({
                type: 'increase_savings',
                description: `Increase monthly savings by ₹${additionalSavings.toFixed(0)}`,
                impact: 'get_back_on_track'
            });
        }

        options.push({
            type: 'reduce_expenses',
            description: 'Identify and reduce discretionary spending',
            impact: 'free_up_money_for_goal'
        });

        options.push({
            type: 'increase_income',
            description: 'Explore additional income sources',
            impact: 'accelerate_goal_achievement'
        });

        return options;
    }

    // Convert priority string to number for database storage
    convertPriorityToNumber(priority) {
        const priorityMap = {
            'high': 5,
            'medium': 3,
            'low': 1
        };
        
        if (typeof priority === 'number') {
            return Math.min(Math.max(priority, 1), 5);
        }
        
        return priorityMap[priority] || 3; // Default to medium (3)
    }
}

module.exports = new RecommendationEngine();



