const express = require('express');
const { body } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const currencyService = require('../services/currencyService');
const logger = require('../utils/logger');

const router = express.Router();

// Get supported currencies
router.get('/supported', async (req, res, next) => {
    try {
        const currencies = await currencyService.getSupportedCurrencies();
        res.json({ currencies });
    } catch (error) {
        logger.error('Failed to get supported currencies:', error);
        res.status(500).json({ error: 'Failed to fetch supported currencies' });
    }
});

// Get exchange rates for a base currency
router.get('/rates/:baseCurrency', async (req, res, next) => {
    try {
        const { baseCurrency } = req.params;
        
        // Validate currency code format
        if (!baseCurrency || baseCurrency.length !== 3) {
            return res.status(400).json({ error: 'Base currency must be a valid 3-letter code' });
        }
        
        const rates = await currencyService.getExchangeRates(baseCurrency.toUpperCase());
        res.json(rates);
    } catch (error) {
        logger.error('Failed to get exchange rates:', error);
        res.status(500).json({ error: 'Failed to fetch exchange rates' });
    }
});

// Convert currency amounts
router.post('/convert', [
    body('amount')
        .isNumeric()
        .withMessage('Amount must be a number'),
    body('from')
        .isLength({ min: 3, max: 3 })
        .isAlpha()
        .withMessage('From currency must be a valid 3-letter code'),
    body('to')
        .isLength({ min: 3, max: 3 })
        .isAlpha()
        .withMessage('To currency must be a valid 3-letter code'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const { amount, from, to } = req.body;
        const fromCurrency = from.toUpperCase();
        const toCurrency = to.toUpperCase();
        
        const convertedAmount = await currencyService.convertCurrency(
            parseFloat(amount), 
            fromCurrency, 
            toCurrency
        );
        
        res.json({
            original_amount: parseFloat(amount),
            from_currency: fromCurrency,
            to_currency: toCurrency,
            converted_amount: convertedAmount,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Currency conversion failed:', error);
        res.status(500).json({ 
            error: error.message || 'Currency conversion failed' 
        });
    }
});

module.exports = router;
