const express = require('express');
const { body } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Validation rules for bills
const validateBill = [
    body('title')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Title must be between 1 and 255 characters'),
    body('amount')
        .optional()
        .isNumeric()
        .custom(value => {
            if (value !== undefined && value < 0) {
                throw new Error('Amount must be positive');
            }
            return true;
        }),
    body('due_date')
        .isISO8601()
        .withMessage('Due date must be a valid date'),
    body('frequency')
        .isIn(['once', 'weekly', 'monthly', 'yearly'])
        .withMessage('Frequency must be once, weekly, monthly, or yearly'),
    body('category_id')
        .optional()
        .isUUID()
        .withMessage('Category ID must be a valid UUID'),
    body('reminder_days_before')
        .optional()
        .isInt({ min: 0, max: 30 })
        .withMessage('Reminder days must be between 0 and 30'),
    handleValidationErrors
];

// Get all bill reminders for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { include_paid = 'false', upcoming_only = 'false' } = req.query;

        let query = `
            SELECT 
                b.*,
                c.name as category_name,
                c.color as category_color
            FROM bill_reminders b
            LEFT JOIN categories c ON b.category_id = c.id
            WHERE b.user_id = $1
        `;

        const params = [userId];

        if (include_paid === 'false') {
            query += ' AND b.is_paid = false';
        }

        if (upcoming_only === 'true') {
            query += ' AND b.due_date >= CURRENT_DATE';
            query += ' AND b.due_date <= CURRENT_DATE + INTERVAL \'30 days\'';
        }

        query += ' ORDER BY b.due_date ASC, b.created_at DESC';

        const result = await pool.query(query, params);
        
        const bills = result.rows.map(bill => ({
            ...bill,
            amount: bill.amount ? parseFloat(bill.amount) : null,
            days_until_due: Math.ceil((new Date(bill.due_date) - new Date()) / (1000 * 60 * 60 * 24)),
            should_remind: shouldRemind(bill.due_date, bill.reminder_days_before)
        }));

        res.json({ bills });
    } catch (error) {
        next(error);
    }
});

// Get upcoming bills (next 30 days)
router.get('/upcoming', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { days = 30 } = req.query;

        const query = `
            SELECT 
                b.*,
                c.name as category_name,
                c.color as category_color
            FROM bill_reminders b
            LEFT JOIN categories c ON b.category_id = c.id
            WHERE b.user_id = $1
            AND b.is_paid = false
            AND b.due_date >= CURRENT_DATE
            AND b.due_date <= CURRENT_DATE + INTERVAL '${parseInt(days)} days'
            ORDER BY b.due_date ASC
        `;

        const result = await pool.query(query, [userId]);
        
        const upcomingBills = result.rows.map(bill => ({
            ...bill,
            amount: bill.amount ? parseFloat(bill.amount) : null,
            days_until_due: Math.ceil((new Date(bill.due_date) - new Date()) / (1000 * 60 * 60 * 24)),
            urgency: getBillUrgency(bill.due_date)
        }));

        // Group by urgency
        const grouped = {
            overdue: upcomingBills.filter(bill => bill.days_until_due < 0),
            due_today: upcomingBills.filter(bill => bill.days_until_due === 0),
            due_soon: upcomingBills.filter(bill => bill.days_until_due > 0 && bill.days_until_due <= 7),
            upcoming: upcomingBills.filter(bill => bill.days_until_due > 7)
        };

        res.json({
            upcoming_bills: upcomingBills,
            grouped,
            summary: {
                total_bills: upcomingBills.length,
                total_amount: upcomingBills.reduce((sum, bill) => sum + (bill.amount || 0), 0),
                overdue_count: grouped.overdue.length,
                due_today_count: grouped.due_today.length,
                due_soon_count: grouped.due_soon.length
            }
        });
    } catch (error) {
        next(error);
    }
});

// Create bill reminder
router.post('/', authenticateToken, validateBill, async (req, res, next) => {
    try {
        const userId = req.userId;
        const {
            title,
            amount,
            due_date,
            frequency,
            category_id,
            reminder_days_before = 3
        } = req.body;

        // Verify category belongs to user if specified
        if (category_id) {
            const categoryCheck = await pool.query(
                'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
                [category_id, userId]
            );

            if (categoryCheck.rows.length === 0) {
                return res.status(404).json({ error: 'Category not found' });
            }
        }

        // Create bill reminder
        const result = await pool.query(`
            INSERT INTO bill_reminders (
                user_id, title, amount, due_date, frequency, category_id, reminder_days_before
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `, [userId, title, amount, due_date, frequency, category_id, reminder_days_before]);

        const bill = result.rows[0];

        logger.info(`Bill reminder created: ${bill.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Bill reminder created successfully',
            bill: {
                ...bill,
                amount: bill.amount ? parseFloat(bill.amount) : null
            }
        });
    } catch (error) {
        next(error);
    }
});

// Update bill reminder
router.put('/:id', authenticateToken, validateBill, async (req, res, next) => {
    try {
        const userId = req.userId;
        const billId = req.params.id;
        const {
            title,
            amount,
            due_date,
            frequency,
            category_id,
            reminder_days_before,
            is_paid
        } = req.body;

        // Verify bill exists and belongs to user
        const billCheck = await pool.query(
            'SELECT * FROM bill_reminders WHERE id = $1 AND user_id = $2',
            [billId, userId]
        );

        if (billCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Bill reminder not found' });
        }

        // Verify category belongs to user if specified
        if (category_id) {
            const categoryCheck = await pool.query(
                'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
                [category_id, userId]
            );

            if (categoryCheck.rows.length === 0) {
                return res.status(404).json({ error: 'Category not found' });
            }
        }

        // Update bill reminder
        const result = await pool.query(`
            UPDATE bill_reminders SET
                title = $3,
                amount = $4,
                due_date = $5,
                frequency = $6,
                category_id = $7,
                reminder_days_before = $8,
                is_paid = COALESCE($9, is_paid)
            WHERE id = $1 AND user_id = $2
            RETURNING *
        `, [
            billId, userId, title, amount, due_date, 
            frequency, category_id, reminder_days_before, is_paid
        ]);

        const bill = result.rows[0];

        logger.info(`Bill reminder updated: ${billId} for user: ${userId}`);

        res.json({
            message: 'Bill reminder updated successfully',
            bill: {
                ...bill,
                amount: bill.amount ? parseFloat(bill.amount) : null
            }
        });
    } catch (error) {
        next(error);
    }
});

// Mark bill as paid
router.post('/:id/mark-paid', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const billId = req.params.id;
        const { create_transaction = false, account_id } = req.body;

        // Get bill details
        const billResult = await pool.query(
            'SELECT * FROM bill_reminders WHERE id = $1 AND user_id = $2',
            [billId, userId]
        );

        if (billResult.rows.length === 0) {
            return res.status(404).json({ error: 'Bill reminder not found' });
        }

        const bill = billResult.rows[0];

        if (bill.is_paid) {
            return res.status(400).json({ error: 'Bill is already marked as paid' });
        }

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Mark bill as paid
            await client.query(
                'UPDATE bill_reminders SET is_paid = true WHERE id = $1',
                [billId]
            );

            let transactionId = null;

            // Create transaction if requested
            if (create_transaction && account_id && bill.amount) {
                // Verify account belongs to user
                const accountCheck = await client.query(
                    'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
                    [account_id, userId]
                );

                if (accountCheck.rows.length === 0) {
                    throw new Error('Account not found');
                }

                // Create expense transaction
                const transactionResult = await client.query(`
                    INSERT INTO transactions (
                        user_id, account_id, category_id, amount, type, description, transaction_date
                    ) VALUES ($1, $2, $3, $4, 'expense', $5, $6)
                    RETURNING id
                `, [
                    userId, 
                    account_id, 
                    bill.category_id, 
                    bill.amount, 
                    `Bill payment: ${bill.title}`,
                    bill.due_date
                ]);

                transactionId = transactionResult.rows[0].id;

                // Update account balance
                await client.query(
                    'UPDATE accounts SET balance = balance - $1 WHERE id = $2',
                    [bill.amount, account_id]
                );
            }

            // Create next occurrence for recurring bills
            if (bill.frequency !== 'once') {
                const nextDueDate = calculateNextDueDate(bill.due_date, bill.frequency);
                
                await client.query(`
                    INSERT INTO bill_reminders (
                        user_id, title, amount, due_date, frequency, category_id, 
                        reminder_days_before, is_paid
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, false)
                `, [
                    userId, bill.title, bill.amount, nextDueDate, bill.frequency,
                    bill.category_id, bill.reminder_days_before
                ]);
            }

            await client.query('COMMIT');

            logger.info(`Bill marked as paid: ${billId} for user: ${userId}`);

            res.json({
                message: 'Bill marked as paid successfully',
                transaction_created: !!transactionId,
                transaction_id: transactionId,
                next_occurrence: bill.frequency !== 'once' ? calculateNextDueDate(bill.due_date, bill.frequency) : null
            });
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    } catch (error) {
        next(error);
    }
});

// Delete bill reminder
router.delete('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const billId = req.params.id;

        const result = await pool.query(
            'DELETE FROM bill_reminders WHERE id = $1 AND user_id = $2 RETURNING *',
            [billId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Bill reminder not found' });
        }

        logger.info(`Bill reminder deleted: ${billId} for user: ${userId}`);

        res.json({ message: 'Bill reminder deleted successfully' });
    } catch (error) {
        next(error);
    }
});

// Helper function to determine if a reminder should be sent
function shouldRemind(dueDate, reminderDaysBefore) {
    const today = new Date();
    const due = new Date(dueDate);
    const daysDifference = Math.ceil((due - today) / (1000 * 60 * 60 * 24));
    
    return daysDifference <= reminderDaysBefore && daysDifference >= 0;
}

// Helper function to get bill urgency
function getBillUrgency(dueDate) {
    const daysUntilDue = Math.ceil((new Date(dueDate) - new Date()) /  (1000 * 60 * 60 * 24));
    
    if (daysUntilDue < 0) return 'overdue';
    if (daysUntilDue === 0) return 'due_today';
    if (daysUntilDue <= 3) return 'urgent';
    if (daysUntilDue <= 7) return 'soon';
    return 'upcoming';
}

// Helper function to calculate next due date for recurring bills
function calculateNextDueDate(currentDueDate, frequency) {
    const date = new Date(currentDueDate);
    
    switch (frequency) {
        case 'weekly':
            date.setDate(date.getDate() + 7);
            break;
        case 'monthly':
            date.setMonth(date.getMonth() + 1);
            break;
        case 'yearly':
            date.setFullYear(date.getFullYear() + 1);
            break;
        default:
            return null;
    }
    
    return date;
}

module.exports = router;