-- Add currency constraints and indexes for better data integrity and performance

-- Add constraint to ensure currency is always uppercase and 3 characters
ALTER TABLE accounts 
ADD CONSTRAINT accounts_currency_format 
CHECK (currency ~ '^[A-Z]{3}$');

-- Add index for better performance on currency queries
CREATE INDEX IF NOT EXISTS idx_accounts_currency ON accounts(currency);
CREATE INDEX IF NOT EXISTS idx_accounts_user_currency ON accounts(user_id, currency);

-- Update existing data to ensure it meets the constraint
UPDATE accounts 
SET currency = UPPER(currency) 
WHERE currency IS NOT NULL AND currency != UPPER(currency);

-- Set default currency for any NULL values
UPDATE accounts 
SET currency = 'USD' 
WHERE currency IS NULL;

-- Make currency NOT NULL since it should always have a value
ALTER TABLE accounts 
ALTER COLUMN currency SET NOT NULL;
