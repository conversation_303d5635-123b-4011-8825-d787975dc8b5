const express = require('express');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { pool } = require('../config/database');
const financialAnalyzer = require('../services/financialAnalyzer');
const recommendationEngine = require('../services/recommendationEngine');
const logger = require('../utils/logger');

const router = express.Router();

// Get real-time insights for dashboard
router.get('/dashboard', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get quick financial overview
        const overviewQuery = `
            SELECT 
                (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = $1 AND type = 'income' AND transaction_date >= DATE_TRUNC('month', CURRENT_DATE)) as monthly_income,
                (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND transaction_date >= DATE_TRUNC('month', CURRENT_DATE)) as monthly_expenses,
                (SELECT COUNT(*) FROM ai_recommendations WHERE user_id = $1 AND status = 'active') as active_recommendations,
                (SELECT COUNT(*) FROM goals WHERE user_id = $1 AND status = 'active') as active_goals,
                (SELECT COUNT(*) FROM budgets WHERE user_id = $1 AND is_active = true) as active_budgets
        `;

        const overviewResult = await pool.query(overviewQuery, [userId]);
        const overview = overviewResult.rows[0];

        // Get recent spending trends (last 7 days vs previous 7 days)
        const trendQuery = `
            SELECT 
                (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND transaction_date >= CURRENT_DATE - INTERVAL '7 days') as last_7_days,
                (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND transaction_date >= CURRENT_DATE - INTERVAL '14 days' AND transaction_date < CURRENT_DATE - INTERVAL '7 days') as previous_7_days
        `;

        const trendResult = await pool.query(trendQuery, [userId]);
        const trend = trendResult.rows[0];

        // Get top spending categories this month
        const topCategoriesQuery = `
            SELECT 
                c.name as category_name,
                c.color,
                SUM(t.amount) as total_amount,
                COUNT(t.id) as transaction_count
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.user_id = $1 
            AND t.type = 'expense'
            AND t.transaction_date >= DATE_TRUNC('month', CURRENT_DATE)
            GROUP BY c.name, c.color
            ORDER BY total_amount DESC
            LIMIT 5
        `;

        const topCategoriesResult = await pool.query(topCategoriesQuery, [userId]);

        // Get upcoming bills
        const upcomingBillsQuery = `
            SELECT 
                title,
                amount,
                due_date,
                (due_date::date - CURRENT_DATE) as days_until_due
            FROM bill_reminders
            WHERE user_id = $1 
            AND is_paid = false
            AND due_date >= CURRENT_DATE
            AND due_date <= CURRENT_DATE + INTERVAL '7 days'
            ORDER BY due_date ASC
            LIMIT 3
        `;

        const upcomingBillsResult = await pool.query(upcomingBillsQuery, [userId]);

        // Calculate insights
        const monthlyIncome = parseFloat(overview.monthly_income);
        const monthlyExpenses = parseFloat(overview.monthly_expenses);
        const monthlySavings = monthlyIncome - monthlyExpenses;
        const savingsRate = monthlyIncome > 0 ? (monthlySavings / monthlyIncome) * 100 : 0;

        const last7Days = parseFloat(trend.last_7_days);
        const previous7Days = parseFloat(trend.previous_7_days);
        const spendingChange = previous7Days > 0 ? ((last7Days - previous7Days) / previous7Days) * 100 : 0;

        res.json({
            overview: {
                monthly_income: monthlyIncome,
                monthly_expenses: monthlyExpenses,
                monthly_savings: monthlySavings,
                savings_rate: savingsRate,
                active_recommendations: parseInt(overview.active_recommendations),
                active_goals: parseInt(overview.active_goals),
                active_budgets: parseInt(overview.active_budgets)
            },
            spending_trend: {
                last_7_days: last7Days,
                previous_7_days: previous7Days,
                change_percentage: spendingChange,
                trend_direction: spendingChange > 5 ? 'increasing' : spendingChange < -5 ? 'decreasing' : 'stable'
            },
            top_categories: topCategoriesResult.rows.map(cat => ({
                name: cat.category_name || 'Uncategorized',
                color: cat.color,
                amount: parseFloat(cat.total_amount),
                transaction_count: parseInt(cat.transaction_count)
            })),
            upcoming_bills: upcomingBillsResult.rows.map(bill => ({
                title: bill.title,
                amount: parseFloat(bill.amount || 0),
                due_date: bill.due_date,
                days_until_due: parseInt(bill.days_until_due)
            })),
            quick_insights: generateQuickInsights(overview, trend, savingsRate, spendingChange)
        });
    } catch (error) {
        next(error);
    }
});

// Get weekly financial report
router.get('/weekly-report', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get weekly spending by category
        const weeklySpendingQuery = `
            SELECT 
                c.name as category_name,
                c.color,
                SUM(t.amount) as weekly_amount,
                COUNT(t.id) as transaction_count,
                AVG(t.amount) as avg_transaction
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.user_id = $1 
            AND t.type = 'expense'
            AND t.transaction_date >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY c.name, c.color
            ORDER BY weekly_amount DESC
        `;

        const weeklySpendingResult = await pool.query(weeklySpendingQuery, [userId]);

        // Get daily spending pattern - FIXED: Proper casting for EXTRACT function
        const dailyPatternQuery = `
            SELECT 
                EXTRACT(DOW FROM transaction_date) as day_of_week,
                SUM(amount) as daily_amount,
                COUNT(*) as transaction_count
            FROM transactions
            WHERE user_id = $1 
            AND type = 'expense'
            AND transaction_date >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY EXTRACT(DOW FROM transaction_date)
            ORDER BY day_of_week
        `;

        const dailyPatternResult = await pool.query(dailyPatternQuery, [userId]);

        // Get goal progress this week
        const goalProgressQuery = `
            SELECT 
                g.title,
                g.target_amount,
                g.target_date,
                COALESCE(SUM(gc.amount), 0) as contributed_this_week
            FROM goals g
            LEFT JOIN goal_contributions gc ON g.id = gc.goal_id 
                AND gc.created_at >= CURRENT_DATE - INTERVAL '7 days'
            WHERE g.user_id = $1 AND g.status = 'active'
            GROUP BY g.id, g.title, g.target_amount, g.target_date
        `;

        const goalProgressResult = await pool.query(goalProgressQuery, [userId]);

        // Calculate weekly totals
        const weeklyTotal = weeklySpendingResult.rows.reduce((sum, cat) => sum + parseFloat(cat.weekly_amount), 0);
        const transactionCount = weeklySpendingResult.rows.reduce((sum, cat) => sum + parseInt(cat.transaction_count), 0);

        res.json({
            period: 'Last 7 days',
            summary: {
                total_spent: weeklyTotal,
                transaction_count: transactionCount,
                avg_transaction: transactionCount > 0 ? weeklyTotal / transactionCount : 0,
                daily_average: weeklyTotal / 7
            },
            spending_by_category: weeklySpendingResult.rows.map(cat => ({
                category: cat.category_name || 'Uncategorized',
                color: cat.color,
                amount: parseFloat(cat.weekly_amount),
                transaction_count: parseInt(cat.transaction_count),
                avg_transaction: parseFloat(cat.avg_transaction),
                percentage: weeklyTotal > 0 ? (parseFloat(cat.weekly_amount) / weeklyTotal) * 100 : 0
            })),
            daily_pattern: formatDailyPattern(dailyPatternResult.rows),
            goal_progress: goalProgressResult.rows.map(goal => ({
                title: goal.title,
                target_amount: parseFloat(goal.target_amount),
                contributed_this_week: parseFloat(goal.contributed_this_week),
                target_date: goal.target_date
            })),
            insights: generateWeeklyInsights(weeklySpendingResult.rows, dailyPatternResult.rows, weeklyTotal)
        });
    } catch (error) {
        next(error);
    }
});

// Get monthly deep analysis (Premium)
router.get('/monthly-report', authenticateToken, requirePremium('advanced_analytics'), async (req, res, next) => {
    try {
        const userId = req.userId;

        // Build comprehensive financial profile
        const financialProfile = await financialAnalyzer.buildFinancialProfile(userId);

        // Get monthly comparisons (current vs previous month)
        const monthlyComparisonQuery = `
            SELECT 
                'current' as period,
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expenses
            FROM transactions
            WHERE user_id = $1 
            AND transaction_date >= DATE_TRUNC('month', CURRENT_DATE)
            
            UNION ALL
            
            SELECT 
                'previous' as period,
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expenses
            FROM transactions
            WHERE user_id = $1 
            AND transaction_date >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
            AND transaction_date < DATE_TRUNC('month', CURRENT_DATE)
        `;

        const comparisonResult = await pool.query(monthlyComparisonQuery, [userId]);
        const currentMonth = comparisonResult.rows.find(r => r.period === 'current');
        const previousMonth = comparisonResult.rows.find(r => r.period === 'previous');

        // Get budget performance
        const budgetPerformanceQuery = `
            SELECT 
                b.amount as budget_amount,
                c.name as category_name,
                COALESCE(SUM(t.amount), 0) as spent_amount,
                b.alert_threshold
            FROM budgets b
            LEFT JOIN categories c ON b.category_id = c.id
            LEFT JOIN transactions t ON t.category_id = c.id 
                AND t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date BETWEEN b.start_date AND b.end_date
            WHERE b.user_id = $1 
            AND b.is_active = true
            AND CURRENT_DATE BETWEEN b.start_date AND b.end_date
            GROUP BY b.id, b.amount, c.name, b.alert_threshold
        `;

        const budgetPerformanceResult = await pool.query(budgetPerformanceQuery, [userId]);

        // Generate spending analysis
        const spendingAnalysis = await recommendationEngine.analyzeSpendingPatterns(userId, 30);

        res.json({
            period: 'Current month',
            financial_profile: financialProfile,
            monthly_comparison: {
                current: {
                    income: parseFloat(currentMonth?.income || 0),
                    expenses: parseFloat(currentMonth?.expenses || 0),
                    savings: parseFloat(currentMonth?.income || 0) - parseFloat(currentMonth?.expenses || 0)
                },
                previous: {
                    income: parseFloat(previousMonth?.income || 0),
                    expenses: parseFloat(previousMonth?.expenses || 0),
                    savings: parseFloat(previousMonth?.income || 0) - parseFloat(previousMonth?.expenses || 0)
                },
                changes: {
                    income_change: calculatePercentageChange(
                        parseFloat(previousMonth?.income || 0),
                        parseFloat(currentMonth?.income || 0)
                    ),
                    expense_change: calculatePercentageChange(
                        parseFloat(previousMonth?.expenses || 0),
                        parseFloat(currentMonth?.expenses || 0)
                    )
                }
            },
            budget_performance: budgetPerformanceResult.rows.map(budget => {
                const budgetAmount = parseFloat(budget.budget_amount);
                const spentAmount = parseFloat(budget.spent_amount);
                const utilizationRate = budgetAmount > 0 ? (spentAmount / budgetAmount) : 0;
                
                return {
                    category: budget.category_name,
                    budget_amount: budgetAmount,
                    spent_amount: spentAmount,
                    remaining_amount: budgetAmount - spentAmount,
                    utilization_rate: utilizationRate * 100,
                    status: utilizationRate > 1 ? 'over_budget' : 
                           utilizationRate > budget.alert_threshold ? 'approaching_limit' : 'on_track'
                };
            }),
            spending_analysis: {
                optimization_opportunities: spendingAnalysis.optimization_opportunities.slice(0, 5),
                unusual_transactions: spendingAnalysis.unusual_transactions.slice(0, 3),
                recurring_patterns: spendingAnalysis.recurring_patterns.slice(0, 5)
            },
            recommendations: await getMonthlyRecommendations(userId, financialProfile)
        });
    } catch (error) {
        next(error);
    }
});

// Get recommendation impact analysis (Premium)
router.get('/recommendation-impact', authenticateToken, requirePremium('advanced_analytics'), async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get acted upon recommendations and their impact
        const impactQuery = `
            SELECT 
                r.id,
                r.title,
                r.type,
                r.potential_savings,
                r.created_at,
                ra.created_at as acted_at,
                ra.action_data
            FROM ai_recommendations r
            JOIN recommendation_actions ra ON r.id = ra.recommendation_id
            WHERE r.user_id = $1 
            AND ra.action_type = 'acted_upon'
            AND r.created_at >= CURRENT_DATE - INTERVAL '90 days'
            ORDER BY ra.created_at DESC
        `;

        const impactResult = await pool.query(impactQuery, [userId]);

        // Calculate actual savings by analyzing spending changes
        const impactAnalysis = [];
        
        for (const rec of impactResult.rows) {
            const beforePeriod = new Date(rec.acted_at);
            beforePeriod.setDate(beforePeriod.getDate() - 30);
            
            const afterPeriod = new Date(rec.acted_at);
            afterPeriod.setDate(afterPeriod.getDate() + 30);

            // Get spending before and after acting on recommendation
            const spendingComparisonQuery = `
                SELECT 
                    'before' as period,
                    COALESCE(SUM(amount), 0) as total_spending
                FROM transactions
                WHERE user_id = $1 
                AND type = 'expense'
                AND transaction_date BETWEEN $2 AND $3
                
                UNION ALL
                
                SELECT 
                    'after' as period,
                    COALESCE(SUM(amount), 0) as total_spending
                FROM transactions
                WHERE user_id = $1 
                AND type = 'expense'
                AND transaction_date BETWEEN $4 AND $5
            `;

            const spendingResult = await pool.query(spendingComparisonQuery, [
                userId, beforePeriod, rec.acted_at, rec.acted_at, afterPeriod
            ]);

            const beforeSpending = parseFloat(spendingResult.rows.find(r => r.period === 'before')?.total_spending || 0);
            const afterSpending = parseFloat(spendingResult.rows.find(r => r.period === 'after')?.total_spending || 0);
            const actualSavings = beforeSpending - afterSpending;

            impactAnalysis.push({
                recommendation: {
                    id: rec.id,
                    title: rec.title,
                    type: rec.type,
                    potential_savings: parseFloat(rec.potential_savings),
                    acted_at: rec.acted_at
                },
                impact: {
                    before_spending: beforeSpending,
                    after_spending: afterSpending,
                    actual_savings: actualSavings,
                    savings_rate: beforeSpending > 0 ? (actualSavings / beforeSpending) * 100 : 0,
                    effectiveness: rec.potential_savings > 0 ? (actualSavings / rec.potential_savings) * 100 : 0
                }
            });
        }

        // Calculate overall impact
        const totalPotentialSavings = impactAnalysis.reduce((sum, item) => sum + item.recommendation.potential_savings, 0);
        const totalActualSavings = impactAnalysis.reduce((sum, item) => sum + item.impact.actual_savings, 0);
        const overallEffectiveness = totalPotentialSavings > 0 ? (totalActualSavings / totalPotentialSavings) * 100 : 0;

        res.json({
            period: '90 days',
            summary: {
                recommendations_acted_upon: impactAnalysis.length,
                total_potential_savings: totalPotentialSavings,
                total_actual_savings: totalActualSavings,
                overall_effectiveness: overallEffectiveness,
                avg_effectiveness: impactAnalysis.length > 0 ? 
                    impactAnalysis.reduce((sum, item) => sum + item.impact.effectiveness, 0) / impactAnalysis.length : 0
            },
            detailed_impact: impactAnalysis,
            insights: generateImpactInsights(impactAnalysis, overallEffectiveness)
        });
    } catch (error) {
        next(error);
    }
});

// Helper functions
function generateQuickInsights(overview, trend, savingsRate, spendingChange) {
    const insights = [];

    if (savingsRate < 10) {
        insights.push({
            type: 'warning',
            message: `Your savings rate is ${savingsRate.toFixed(1)}%. Consider increasing it to at least 10%.`
        });
    } else if (savingsRate > 20) {
        insights.push({
            type: 'success',
            message: `Excellent! Your savings rate of ${savingsRate.toFixed(1)}% is above the recommended 20%.`
        });
    }

    if (spendingChange > 15) {
        insights.push({
            type: 'alert',
            message: `Your spending increased by ${spendingChange.toFixed(1)}% this week. Review your recent expenses.`
        });
    } else if (spendingChange < -10) {
        insights.push({
            type: 'success',
            message: `Great job! Your spending decreased by ${Math.abs(spendingChange).toFixed(1)}% this week.`
        });
    }

    if (parseInt(overview.active_recommendations) > 0) {
        insights.push({
            type: 'info',
            message: `You have ${overview.active_recommendations} active recommendations to help optimize your finances.`
        });
    }

    return insights;
}

function formatDailyPattern(dailyData) {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    return dayNames.map((dayName, index) => {
        const dayData = dailyData.find(d => parseInt(d.day_of_week) === index);
        return {
            day: dayName,
            amount: parseFloat(dayData?.daily_amount || 0),
            transaction_count: parseInt(dayData?.transaction_count || 0)
        };
    });
}

function generateWeeklyInsights(categoryData, dailyData, weeklyTotal) {
    const insights = [];

    // Top spending category insight
    if (categoryData.length > 0) {
        const topCategory = categoryData[0];
        const percentage = (parseFloat(topCategory.weekly_amount) / weeklyTotal) * 100;
        insights.push({
            type: 'info',
            message: `${topCategory.category_name || 'Uncategorized'} was your biggest expense this week (${percentage.toFixed(1)}% of total spending).`
        });
    }

    // Daily pattern insight
    const dailyAmounts = dailyData.map(d => parseFloat(d.daily_amount));
    const maxDay = dailyAmounts.indexOf(Math.max(...dailyAmounts));
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    if (dailyAmounts[maxDay] > 0) {
        insights.push({
            type: 'info',
            message: `${dayNames[maxDay]} was your highest spending day this week.`
        });
    }

    return insights;
}

function calculatePercentageChange(oldValue, newValue) {
    if (oldValue === 0) return newValue > 0 ? 100 : 0;
    return ((newValue - oldValue) / oldValue) * 100;
}

async function getMonthlyRecommendations(userId, financialProfile) {
    try {
        // Get recent recommendations
        const recentRecsQuery = `
            SELECT * FROM ai_recommendations 
            WHERE user_id = $1 
            AND status = 'active'
            AND created_at >= CURRENT_DATE - INTERVAL '30 days'
            ORDER BY priority DESC, created_at DESC
            LIMIT 3
        `;

        const recentRecsResult = await pool.query(recentRecsQuery, [userId]);
        return recentRecsResult.rows.map(rec => ({
            title: rec.title,
            description: rec.description,
            potential_savings: parseFloat(rec.potential_savings || 0),
            priority: rec.priority
        }));
    } catch (error) {
        logger.error('Failed to get monthly recommendations:', error);
        return [];
    }
}

function generateImpactInsights(impactAnalysis, overallEffectiveness) {
    const insights = [];

    if (overallEffectiveness > 80) {
        insights.push({
            type: 'success',
            message: `Excellent! Your recommendations are ${overallEffectiveness.toFixed(1)}% effective on average.`
        });
    } else if (overallEffectiveness > 50) {
        insights.push({
            type: 'info',
            message: `Good progress! Your recommendations are ${overallEffectiveness.toFixed(1)}% effective. Keep following the advice.`
        });
    } else if (overallEffectiveness > 0) {
        insights.push({
            type: 'warning',
            message: `Your recommendations are ${overallEffectiveness.toFixed(1)}% effective. Consider reviewing your implementation approach.`
        });
    }

    if (impactAnalysis.length > 0) {
        const bestPerforming = impactAnalysis.reduce((best, current) => 
            current.impact.effectiveness > best.impact.effectiveness ? current : best
        , impactAnalysis[0]);

        if (bestPerforming && bestPerforming.impact.effectiveness > 100) {
            insights.push({
                type: 'success',
                message: `"${bestPerforming.recommendation.title}" exceeded expectations with ${bestPerforming.impact.effectiveness.toFixed(1)}% effectiveness!`
            });
        }
    }

    return insights;
}

module.exports = router;