const { Pool } = require('pg');

async function checkDatabaseConnection() {
    console.log('🔍 Checking database connections...\n');
    
    // Check PostgreSQL
    console.log('📊 PostgreSQL Connection Test:');
    const pool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'finance_manager',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || 'password123',
        connectionTimeoutMillis: 5000,
    });
    
    try {
        const client = await pool.connect();
        const result = await client.query('SELECT NOW() as current_time, version() as version');
        client.release();
        
        console.log('✅ PostgreSQL: Connected successfully');
        console.log(`   Time: ${result.rows[0].current_time}`);
        console.log(`   Version: ${result.rows[0].version.split(' ').slice(0, 2).join(' ')}`);
        
        // Test table creation capability
        try {
            const testResult = await pool.query('SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = $1', ['public']);
            console.log(`   Tables: ${testResult.rows[0].count} tables found`);
        } catch (error) {
            console.log(`   Tables: Unable to query (${error.message})`);
        }
        
    } catch (error) {
        console.log('❌ PostgreSQL: Connection failed');
        console.log(`   Error: ${error.message}`);
        console.log('   💡 Try: npm run db:start');
    } finally {
        await pool.end();
    }
    
    console.log('\n🔴 Redis Connection Test:');
    
    // Check Redis with a simpler approach
    try {
        // Try to connect using a basic TCP connection test first
        const net = require('net');
        const redisHost = process.env.REDIS_HOST || 'localhost';
        const redisPort = process.env.REDIS_PORT || 6379;
        
        await new Promise((resolve, reject) => {
            const socket = net.createConnection(redisPort, redisHost);
            
            socket.on('connect', () => {
                socket.end();
                resolve();
            });
            
            socket.on('error', (error) => {
                reject(error);
            });
            
            setTimeout(() => {
                socket.destroy();
                reject(new Error('Connection timeout'));
            }, 5000);
        });
        
        console.log('✅ Redis: Port is accessible');
        console.log(`   Host: ${redisHost}:${redisPort}`);
        
    } catch (error) {
        console.log('❌ Redis: Connection failed');
        console.log(`   Error: ${error.message}`);
        console.log('   💡 Try: npm run db:start');
    }
    
    console.log('\n🎯 Connection Summary Complete');
    console.log('\n📝 If connections failed:');
    console.log('1. Run: npm run db:start');
    console.log('2. Wait for containers to be ready');
    console.log('3. Check Docker Desktop is running');
}

// Load environment variables
require('dotenv').config();

checkDatabaseConnection().catch(console.error);