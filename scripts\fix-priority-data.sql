-- Update existing goals with string priorities to integers
UPDATE goals 
SET priority = CASE 
    WHEN priority::text = 'high' THEN 3
    WHEN priority::text = 'medium' THEN 2  
    WHEN priority::text = 'low' THEN 1
    ELSE 2
END
WHERE priority::text IN ('high', 'medium', 'low');

-- Update existing recommendations with string priorities to integers  
UPDATE ai_recommendations
SET priority = CASE
    WHEN priority::text = 'high' THEN 5
    WHEN priority::text = 'medium' THEN 3
    WHEN priority::text = 'low' THEN 1
    ELSE 3
END
WHERE priority::text IN ('high', 'medium', 'low');