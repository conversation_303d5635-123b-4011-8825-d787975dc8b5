const { aiCategorizationQueue } = require('../config/queue');
const { pool } = require('../config/database');
const aiCategorizer = require('../services/aiCategorizer');
const logger = require('../utils/logger');

// Process AI categorization
aiCategorizationQueue.process('categorize-transactions', async (job) => {
    const { uploadId, userId, transactions } = job.data;
    
    try {
        logger.info(`Starting AI categorization for upload: ${uploadId} (${transactions.length} transactions)`);
        
        // Update status
        await pool.query(
            'UPDATE statement_uploads SET status = $1, updated_at = NOW() WHERE id = $2',
            ['categorizing', uploadId]
        );

        // Get user's categories
        const categoriesResult = await pool.query(
            'SELECT name, color FROM categories WHERE user_id = $1 AND is_active = true',
            [userId]
        );
        
        const userCategories = categoriesResult.rows;

        // Categorize transactions with AI
        const categorizedResults = await aiCategorizer.categorizeTransactions(transactions, userCategories);
        
        // Merge AI results with original transactions
        const enhancedTransactions = transactions.map((transaction, index) => {
            const aiResult = categorizedResults[index] || {
                category: 'Uncategorized',
                confidence: 0.1,
                merchant: null
            };
            
            return {
                ...transaction,
                suggested_category: aiResult.category,
                confidence_score: aiResult.confidence,
                suggested_merchant: aiResult.merchant || transaction.merchant
            };
        });

        // Get current processed data and update with AI results
        const uploadResult = await pool.query(
            'SELECT processed_data FROM statement_uploads WHERE id = $1',
            [uploadId]
        );
        
        const processedData = uploadResult.rows[0].processed_data;
        processedData.transactions = enhancedTransactions;
        
        // Add AI categorization summary
        processedData.ai_summary = {
            total_categorized: enhancedTransactions.length,
            high_confidence: enhancedTransactions.filter(t => t.confidence_score >= 0.8).length,
            medium_confidence: enhancedTransactions.filter(t => t.confidence_score >= 0.5 && t.confidence_score < 0.8).length,
            low_confidence: enhancedTransactions.filter(t => t.confidence_score < 0.5).length,
            categories_used: [...new Set(enhancedTransactions.map(t => t.suggested_category))],
            processed_at: new Date().toISOString()
        };

        // Update database with categorized data
        await pool.query(`
            UPDATE statement_uploads 
            SET 
                status = $1,
                processed_data = $2,
                updated_at = NOW()
            WHERE id = $3
        `, ['ready', JSON.stringify(processedData), uploadId]);

        logger.info(`AI categorization completed for upload: ${uploadId}`);
        
        return {
            success: true,
            uploadId: uploadId,
            categorizedCount: enhancedTransactions.length,
            highConfidenceCount: processedData.ai_summary.high_confidence
        };

    } catch (error) {
        logger.error(`AI categorization failed for upload ${uploadId}:`, error);
        
        // Update status to ready (without AI categorization)
        await pool.query(
            'UPDATE statement_uploads SET status = $1, updated_at = NOW() WHERE id = $2',
            ['ready', uploadId]
        );
        
        // Don't throw error - allow import to proceed without AI categorization
        return {
            success: false,
            uploadId: uploadId,
            error: error.message
        };
    }
});

logger.info('AI categorization worker started');