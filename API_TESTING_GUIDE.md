# 🧪 Finance Manager API Testing Guide

## 📋 Prerequisites

1. **Run the seed script first:**
   ```bash
   npm run db:seed
   ```

2. **Test user credentials (created by seed):**
   - Email: `<EMAIL>`
   - Password: `testpassword123`
   - Subscription: Premium

3. **Base URL:** `http://localhost:3000`

---

## 🔐 Authentication Endpoints

### 1. Register User
**Description:** Create a new user account
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 2. Login
**Description:** Authenticate user and get access tokens
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'
```
**Save the `access_token` from response for subsequent requests!**

### 3. Refresh Token
**Description:** Get new access token using refresh token
```bash
curl -X POST http://localhost:3000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

### 4. Logout
**Description:** Invalidate refresh token
```bash
curl -X POST http://localhost:3000/api/auth/logout \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

---

## 👤 User Management Endpoints

### 5. Get User Profile
**Description:** Get current user's profile and stats
```bash
curl -X GET http://localhost:3000/api/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 6. Update User Profile
**Description:** Update user's email address
```bash
curl -X PUT http://localhost:3000/api/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### 7. Get Subscription Status
**Description:** Get user's subscription tier and usage limits
```bash
curl -X GET http://localhost:3000/api/user/subscription-status \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 8. Upgrade to Premium
**Description:** Upgrade user to premium subscription (mock implementation)
```bash
curl -X POST http://localhost:3000/api/user/upgrade-premium \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "pm_test_123456"
  }'
```

### 9. Cancel Premium
**Description:** Cancel premium subscription
```bash
curl -X POST http://localhost:3000/api/user/cancel-premium \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 🏦 Account Management Endpoints

### 10. Get All Accounts
**Description:** Get all user's accounts with transaction counts
```bash
curl -X GET http://localhost:3000/api/accounts \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 11. Get Account with Balance History
**Description:** Get accounts with 30-day balance history
```bash
curl -X GET "http://localhost:3000/api/accounts?include_balance_history=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 12. Get Single Account
**Description:** Get specific account details
```bash
curl -X GET http://localhost:3000/api/accounts/ACCOUNT_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 13. Create Account
**Description:** Create a new financial account
```bash
curl -X POST http://localhost:3000/api/accounts \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Savings Account",
    "type": "savings",
    "balance": 5000.00,
    "currency": "USD"
  }'
```

### 14. Update Account
**Description:** Update account details
```bash
curl -X PUT http://localhost:3000/api/accounts/ACCOUNT_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Account Name",
    "type": "checking",
    "currency": "USD"
  }'
```

### 15. Delete Account
**Description:** Delete or deactivate account
```bash
curl -X DELETE http://localhost:3000/api/accounts/ACCOUNT_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 16. Get Account Balance History
**Description:** Get 30-day balance history for specific account
```bash
curl -X GET "http://localhost:3000/api/accounts/ACCOUNT_ID/balance-history?days=30" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 17. Get Account Summary
**Description:** Get account summary with spending breakdown
```bash
curl -X GET "http://localhost:3000/api/accounts/ACCOUNT_ID/summary?period=month" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 💰 Transaction Management Endpoints

### 18. Get All Transactions
**Description:** Get paginated transactions with filtering
```bash
curl -X GET "http://localhost:3000/api/transactions?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 19. Get Filtered Transactions
**Description:** Get transactions with date and category filters
```bash
curl -X GET "http://localhost:3000/api/transactions?start_date=2024-01-01&end_date=2024-12-31&type=expense&min_amount=10&max_amount=1000" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 20. Get Calendar View
**Description:** Get transactions grouped by day for calendar view
```bash
curl -X GET http://localhost:3000/api/transactions/calendar/2024/12 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 21. Create Transaction
**Description:** Create a new transaction
```bash
curl -X POST http://localhost:3000/api/transactions \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "ACCOUNT_ID",
    "category_id": "CATEGORY_ID",
    "amount": 50.00,
    "type": "expense",
    "description": "Coffee shop purchase",
    "transaction_date": "2024-12-26",
    "merchant": "Starbucks",
    "location": "Downtown",
    "tags": ["coffee", "morning"]
  }'
```

### 22. Update Transaction
**Description:** Update existing transaction
```bash
curl -X PUT http://localhost:3000/api/transactions/TRANSACTION_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "ACCOUNT_ID",
    "category_id": "CATEGORY_ID",
    "amount": 75.00,
    "type": "expense",
    "description": "Updated coffee purchase",
    "transaction_date": "2024-12-26",
    "merchant": "Local Coffee Shop",
    "tags": ["coffee", "updated"]
  }'
```

### 23. Delete Transaction
**Description:** Delete a transaction
```bash
curl -X DELETE http://localhost:3000/api/transactions/TRANSACTION_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 24. Bulk Import Transactions
**Description:** Import multiple transactions at once
```bash
curl -X POST http://localhost:3000/api/transactions/bulk-import \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "transactions": [
      {
        "account_id": "ACCOUNT_ID",
        "category_id": "CATEGORY_ID",
        "amount": 25.00,
        "type": "expense",
        "description": "Lunch",
        "transaction_date": "2024-12-26",
        "merchant": "Restaurant"
      },
      {
        "account_id": "ACCOUNT_ID",
        "category_id": "CATEGORY_ID",
        "amount": 100.00,
        "type": "expense",
        "description": "Groceries",
        "transaction_date": "2024-12-26",
        "merchant": "Supermarket"
      }
    ]
  }'
```

### 25. Export Transactions
**Description:** Export transactions to Excel format
```bash
curl -X GET "http://localhost:3000/api/transactions/export?format=excel&start_date=2024-01-01&end_date=2024-12-31" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  --output transactions.xlsx
```

---

## 🏷️ Category Management Endpoints

### 26. Get All Categories
**Description:** Get all user's categories
```bash
curl -X GET http://localhost:3000/api/categories \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 27. Get Category Hierarchy
**Description:** Get categories organized in hierarchical structure
```bash
curl -X GET http://localhost:3000/api/categories/hierarchy \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 28. Create Category
**Description:** Create a new category
```bash
curl -X POST http://localhost:3000/api/categories \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Online Shopping",
    "color": "#FF5733",
    "icon": "shopping-cart",
    "parent_id": null
  }'
```

### 29. Create Subcategory
**Description:** Create a subcategory under existing category
```bash
curl -X POST http://localhost:3000/api/categories \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Electronics",
    "color": "#3498DB",
    "icon": "laptop",
    "parent_id": "PARENT_CATEGORY_ID"
  }'
```

### 30. Update Category
**Description:** Update category details
```bash
curl -X PUT http://localhost:3000/api/categories/CATEGORY_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Category Name",
    "color": "#E74C3C",
    "icon": "updated-icon"
  }'
```

### 31. Delete Category
**Description:** Delete or deactivate category
```bash
curl -X DELETE http://localhost:3000/api/categories/CATEGORY_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📊 Budget Management Endpoints

### 32. Get All Budgets
**Description:** Get all user's budgets with spending progress
```bash
curl -X GET http://localhost:3000/api/budgets \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 33. Get Single Budget
**Description:** Get detailed budget with transactions and daily spending
```bash
curl -X GET http://localhost:3000/api/budgets/BUDGET_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 34. Create Budget
**Description:** Create a new budget for a category
```bash
curl -X POST http://localhost:3000/api/budgets \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "category_id": "CATEGORY_ID",
    "amount": 500.00,
    "period": "monthly",
    "start_date": "2024-12-01",
    "end_date": "2024-12-31",
    "alert_threshold": 0.8
  }'
```

### 35. Update Budget
**Description:** Update existing budget
```bash
curl -X PUT http://localhost:3000/api/budgets/BUDGET_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "category_id": "CATEGORY_ID",
    "amount": 600.00,
    "period": "monthly",
    "start_date": "2024-12-01",
    "end_date": "2024-12-31",
    "alert_threshold": 0.9,
    "is_active": true
  }'
```

### 36. Delete Budget
**Description:** Delete a budget
```bash
curl -X DELETE http://localhost:3000/api/budgets/BUDGET_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📅 Bill Reminder Endpoints

### 37. Get All Bills
**Description:** Get all bill reminders
```bash
curl -X GET http://localhost:3000/api/bills \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 38. Get Upcoming Bills
**Description:** Get bills due in next 30 days
```bash
curl -X GET "http://localhost:3000/api/bills/upcoming?days=30" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 39. Create Bill Reminder
**Description:** Create a new bill reminder
```bash
curl -X POST http://localhost:3000/api/bills \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Electric Bill",
    "amount": 120.00,
    "due_date": "2025-01-15",
    "frequency": "monthly",
    "category_id": "CATEGORY_ID",
    "reminder_days_before": 3
  }'
```

### 40. Update Bill Reminder
**Description:** Update existing bill reminder
```bash
curl -X PUT http://localhost:3000/api/bills/BILL_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Electric Bill",
    "amount": 130.00,
    "due_date": "2025-01-15",
    "frequency": "monthly",
    "category_id": "CATEGORY_ID",
    "reminder_days_before": 5,
    "is_paid": false
  }'
```

### 41. Mark Bill as Paid
**Description:** Mark bill as paid and optionally create transaction
```bash
curl -X POST http://localhost:3000/api/bills/BILL_ID/mark-paid \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "create_transaction": true,
    "account_id": "ACCOUNT_ID"
  }'
```

### 42. Delete Bill Reminder
**Description:** Delete a bill reminder
```bash
curl -X DELETE http://localhost:3000/api/bills/BILL_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📈 Analytics Endpoints

### 43. Get Spending Trends
**Description:** Get monthly spending trends for last 6 months
```bash
curl -X GET "http://localhost:3000/api/analytics/spending-trends?months=6" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 44. Get Category Breakdown
**Description:** Get spending breakdown by category for date range
```bash
curl -X GET "http://localhost:3000/api/analytics/category-breakdown?start_date=2024-12-01&end_date=2024-12-31" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 45. Get Predictive Alerts (Premium)
**Description:** Get AI-powered spending alerts and predictions
```bash
curl -X GET http://localhost:3000/api/analytics/predictive-alerts \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 🌟 Premium Features Endpoints

### 46. Get Account Shares (Premium)
**Description:** Get accounts shared with/by user
```bash
curl -X GET http://localhost:3000/api/premium/account-shares \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 47. Share Account (Premium)
**Description:** Share account with another user
```bash
curl -X POST http://localhost:3000/api/premium/account-shares \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "ACCOUNT_ID",
    "share_with_email": "<EMAIL>",
    "permission_level": "view"
  }'
```

### 48. Update Share Permissions (Premium)
**Description:** Update account sharing permissions
```bash
curl -X PUT http://localhost:3000/api/premium/account-shares/SHARE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permission_level": "edit"
  }'
```

### 49. Remove Account Share (Premium)
**Description:** Remove account sharing
```bash
curl -X DELETE http://localhost:3000/api/premium/account-shares/SHARE_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 50. Get Shared Account Details (Premium)
**Description:** Get details of shared account
```bash
curl -X GET http://localhost:3000/api/premium/shared-accounts/ACCOUNT_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 51. Get Premium Analytics Dashboard (Premium)
**Description:** Get comprehensive analytics dashboard
```bash
curl -X GET http://localhost:3000/api/premium/analytics-dashboard \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 📄 Statement Import Endpoints

### 52. Get Supported Formats
**Description:** Get list of supported file formats for import
```bash
curl -X GET http://localhost:3000/api/statements/formats \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 53. Upload Statement File
**Description:** Upload bank statement file for processing
```bash
curl -X POST http://localhost:3000/api/statements/upload \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "statement=@/path/to/your/statement.csv"
```

### 54. Get Upload Status
**Description:** Check processing status of uploaded file
```bash
curl -X GET http://localhost:3000/api/statements/upload/UPLOAD_ID/status \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 55. Preview Transactions
**Description:** Preview parsed transactions before import
```bash
curl -X GET http://localhost:3000/api/statements/upload/UPLOAD_ID/preview \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 56. Start Import
**Description:** Import transactions to account
```bash
curl -X POST http://localhost:3000/api/statements/upload/UPLOAD_ID/import \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "ACCOUNT_ID",
    "skip_duplicates": true,
    "category_mappings": {
      "Food & Dining": "CATEGORY_ID",
      "Transportation": "CATEGORY_ID"
    }
  }'
```

### 57. Get Import Status
**Description:** Check import progress
```bash
curl -X GET http://localhost:3000/api/statements/import/SESSION_ID/status \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 58. Get Upload History
**Description:** Get user's upload history
```bash
curl -X GET "http://localhost:3000/api/statements/history?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 59. Delete Upload
**Description:** Delete uploaded file and associated data
```bash
curl -X DELETE http://localhost:3000/api/statements/upload/UPLOAD_ID \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 💱 Currency Management Endpoints

### 60. Get Supported Currencies
**Description:** Get list of all supported currencies
```bash
curl -X GET http://localhost:3000/api/currency/supported
```

### 61. Get Exchange Rates
**Description:** Get current exchange rates for a base currency
```bash
curl -X GET http://localhost:3000/api/currency/rates/USD
```

### 62. Convert Currency
**Description:** Convert amount between two currencies
```bash
curl -X POST http://localhost:3000/api/currency/convert \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "from": "USD",
    "to": "EUR"
  }'
```

### 63. Get Account Currency Summary
**Description:** Get account totals grouped by currency
```bash
curl -X GET http://localhost:3000/api/accounts/currency-summary \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## 🔧 System Endpoints

### 64. Health Check
**Description:** Check API health status
```bash
curl -X GET http://localhost:3000/health
```

---

## 📝 Testing Workflow

### 1. **Start with Authentication:**
```bash
# Login to get access token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "testpassword123"}'
```

### 2. **Test Core Features:**
- Get user profile
- List accounts (seeded data available)
- List categories (seeded data available)
- Create a transaction
- Create a budget

### 3. **Test Advanced Features:**
- Upload a CSV file
- Test analytics endpoints
- Test premium features

### 4. **Error Testing:**
- Try endpoints without authentication
- Try invalid data
- Test rate limiting

---

## 🚨 Important Notes

1. **Replace placeholders:**
   - `YOUR_ACCESS_TOKEN` - Use token from login response
   - `ACCOUNT_ID` - Use ID from accounts list
   - `CATEGORY_ID` - Use ID from categories list
   - `TRANSACTION_ID` - Use ID from transactions list

2. **File uploads:**
   - Use actual CSV/PDF/OFX files for statement upload testing
   - Max file size: 10MB

3. **Rate limiting:**
   - 100 requests per 15 minutes per IP
   - 20 file uploads per hour per IP

4. **Premium features:**
   - Test user has premium subscription
   - Some endpoints require premium tier

5. **Database:**
   - Run `npm run db:seed` before testing
   - Seeded data includes test accounts, categories, and transactions

---

## 🎯 Quick Test Script

Save this as `test-api.sh` for quick testing:

```bash
#!/bin/bash

# Login and get token
TOKEN=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "testpassword123"}' \
  | jq -r '.tokens.access_token')

echo "Access Token: $TOKEN"

# Test basic endpoints
echo "Testing user profile..."
curl -s -X GET http://localhost:3000/api/user/profile \
  -H "Authorization: Bearer $TOKEN" | jq

echo "Testing accounts..."
curl -s -X GET http://localhost:3000/api/accounts \
  -H "Authorization: Bearer $TOKEN" | jq

echo "Testing categories..."
curl -s -X GET http://localhost:3000/api/categories \
  -H "Authorization: Bearer $TOKEN" | jq
```

Run with: `chmod +x test-api.sh && ./test-api.sh`