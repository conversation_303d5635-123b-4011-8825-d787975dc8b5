const express = require('express');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const analyticsService = require('../services/analyticsService');

const router = express.Router();

// Get spending trends
router.get('/spending-trends', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { months = 6 } = req.query;

        const trends = await analyticsService.getSpendingTrends(userId, parseInt(months));

        res.json({
            period_months: parseInt(months),
            trends: trends.map(trend => ({
                month: trend.month,
                expenses: parseFloat(trend.expenses || 0),
                income: parseFloat(trend.income || 0),
                net: parseFloat(trend.income || 0) - parseFloat(trend.expenses || 0),
                transaction_count: parseInt(trend.transaction_count)
            }))
        });
    } catch (error) {
        next(error);
    }
});

// Get category breakdown
router.get('/category-breakdown', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { start_date, end_date } = req.query;

        if (!start_date || !end_date) {
            return res.status(400).json({ 
                error: 'start_date and end_date are required' 
            });
        }

        const breakdown = await analyticsService.getCategoryBreakdown(
            userId, 
            start_date, 
            end_date
        );

        const totalAmount = breakdown.reduce((sum, category) => 
            sum + parseFloat(category.total_amount), 0
        );

        res.json({
            date_range: {
                start: start_date,
                end: end_date
            },
            total_amount: totalAmount,
            categories: breakdown.map(category => ({
                name: category.category_name,
                color: category.color,
                total_amount: parseFloat(category.total_amount),
                transaction_count: parseInt(category.transaction_count),
                avg_amount: parseFloat(category.avg_amount),
                percentage: totalAmount > 0 ? (parseFloat(category.total_amount) / totalAmount * 100) : 0
            }))
        });
    } catch (error) {
        next(error);
    }
});

// Get predictive alerts (Premium feature)
router.get('/predictive-alerts', authenticateToken, requirePremium('predictive_alerts'), async (req, res, next) => {
    try {
        const userId = req.userId;
        const alerts = await analyticsService.getPredictiveAlerts(userId);

        res.json({
            alerts,
            generated_at: new Date().toISOString()
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;