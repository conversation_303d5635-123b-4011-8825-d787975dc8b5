const { statementProcessingQueue, aiCategorizationQueue } = require('../config/queue');
const { pool } = require('../config/database');
const awsService = require('../config/aws');
const ProcessorFactory = require('../processors/processorFactory');
const logger = require('../utils/logger');

// Process statement files
statementProcessingQueue.process('process-statement', async (job) => {
    const { uploadId, userId, s3Key, fileType, mimeType } = job.data;
    
    try {
        logger.info(`Processing statement: ${uploadId}`);
        
        // Update status to processing
        await pool.query(
            'UPDATE statement_uploads SET status = $1, updated_at = NOW() WHERE id = $2',
            ['processing', uploadId]
        );

        // Get file from S3
        const fileBuffer = await awsService.getFile(s3Key);
        
        // Get appropriate processor
        const processor = ProcessorFactory.getProcessor(fileType, mimeType);
        
        // Process file
        const processingResult = await processor.process(fileBuffer, s3Key);
        
        if (!processingResult.success) {
            throw new Error('File processing failed');
        }

        // Update database with processed data
        await pool.query(`
            UPDATE statement_uploads 
            SET 
                status = $1,
                processed_data = $2,
                total_transactions = $3,
                updated_at = NOW()
            WHERE id = $4
        `, [
            'parsed',
            JSON.stringify(processingResult),
            processingResult.totalCount,
            uploadId
        ]);

        // Queue for AI categorization if there are transactions
        if (processingResult.transactions && processingResult.transactions.length > 0) {
            await aiCategorizationQueue.add('categorize-transactions', {
                uploadId: uploadId,
                userId: userId,
                transactions: processingResult.transactions
            });
        } else {
            // No transactions found, mark as ready
            await pool.query(
                'UPDATE statement_uploads SET status = $1, updated_at = NOW() WHERE id = $2',
                ['ready', uploadId]
            );
        }

        logger.info(`Statement processed successfully: ${uploadId} (${processingResult.totalCount} transactions)`);
        
        return {
            success: true,
            uploadId: uploadId,
            transactionCount: processingResult.totalCount
        };

    } catch (error) {
        logger.error(`Statement processing failed for ${uploadId}:`, error);
        
        // Update status to failed
        await pool.query(`
            UPDATE statement_uploads 
            SET 
                status = $1,
                error_message = $2,
                updated_at = NOW()
            WHERE id = $3
        `, ['failed', error.message, uploadId]);
        
        throw error;
    }
});

logger.info('Statement processor worker started');