const cron = require('cron');
const { pool } = require('../config/database');
const logger = require('./logger');

class Scheduler {
    constructor() {
        this.jobs = [];
    }

    start() {
        // Update exchange rates every hour
        const exchangeRateJob = new cron.CronJob('0 0 * * * *', async () => {
            try {
                logger.info('Updating exchange rates...');
                // Exchange rates are updated on-demand with caching
                // This job could trigger a cache refresh if needed
            } catch (error) {
                logger.error('Failed to update exchange rates:', error);
            }
        });

        // Send bill reminders daily at 9 AM
        const billReminderJob = new cron.CronJob('0 0 9 * * *', async () => {
            try {
                logger.info('Checking for bill reminders...');
                await this.sendBillReminders();
            } catch (error) {
                logger.error('Failed to send bill reminders:', error);
            }
        });

        // Generate predictive alerts daily at 8 AM
        const predictiveAlertsJob = new cron.CronJob('0 0 8 * * *', async () => {
            try {
                logger.info('Generating predictive alerts...');
                await this.generatePredictiveAlerts();
            } catch (error) {
                logger.error('Failed to generate predictive alerts:', error);
            }
        });

        // Clean up expired sessions weekly
        const cleanupJob = new cron.CronJob('0 0 2 * * 0', async () => {
            try {
                logger.info('Cleaning up expired data...');
                await this.cleanupExpiredData();
            } catch (error) {
                logger.error('Failed to cleanup expired data:', error);
            }
        });

        this.jobs = [exchangeRateJob, billReminderJob, predictiveAlertsJob, cleanupJob];
        
        this.jobs.forEach(job => job.start());
        logger.info('Scheduler started with all jobs');
    }

    stop() {
        this.jobs.forEach(job => job.stop());
        logger.info('Scheduler stopped');
    }

    async sendBillReminders() {
        const query = `
            SELECT 
                b.*,
                u.email as user_email
            FROM bill_reminders b
            JOIN users u ON b.user_id = u.id
            WHERE b.is_paid = false
            AND b.due_date >= CURRENT_DATE
            AND b.due_date <= CURRENT_DATE + INTERVAL '1 day' * b.reminder_days_before
        `;

        const result = await pool.query(query);
        const reminders = result.rows;

        for (const reminder of reminders) {
            // TODO: Send email notification
            logger.info(`Bill reminder: ${reminder.title} for ${reminder.user_email}`);
        }

        logger.info(`Processed ${reminders.length} bill reminders`);
    }

    async generatePredictiveAlerts() {
        // Get all premium users
        const usersQuery = `
            SELECT id FROM users 
            WHERE subscription_tier = 'premium' 
            AND (subscription_expires_at IS NULL OR subscription_expires_at > NOW())
        `;

        const usersResult = await pool.query(usersQuery);
        const premiumUsers = usersResult.rows;

        for (const user of premiumUsers) {
            try {
                // Generate alerts for each premium user
                // This would typically store alerts in a notifications table
                logger.info(`Generated predictive alerts for user: ${user.id}`);
            } catch (error) {
                logger.error(`Failed to generate alerts for user ${user.id}:`, error);
            }
        }

        logger.info(`Generated predictive alerts for ${premiumUsers.length} premium users`);
    }

    async cleanupExpiredData() {
        // Clean up expired refresh tokens
        await pool.query(
            'UPDATE users SET refresh_token = NULL WHERE subscription_expires_at < NOW() - INTERVAL \'30 days\''
        );

        // Clean up old audit logs (if implemented)
        // await pool.query('DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL \'1 year\'');

        logger.info('Cleanup completed');
    }
}

module.exports = new Scheduler();