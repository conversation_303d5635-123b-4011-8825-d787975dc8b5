const express = require('express');
const { body } = require('express-validator');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const analyticsService = require('../services/analyticsService');
const logger = require('../utils/logger');

const router = express.Router();

// Get account shares (Premium feature)
router.get('/account-shares', authenticateToken, requirePremium('account_sharing'), async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get shares where user is the owner
        const ownedSharesQuery = `
            SELECT 
                s.*,
                u.email as shared_with_email,
                a.name as account_name,
                a.type as account_type
            FROM account_shares s
            JOIN users u ON s.shared_with_id = u.id
            JOIN accounts a ON s.account_id = a.id
            WHERE s.owner_id = $1
            ORDER BY s.shared_at DESC
        `;

        const ownedShares = await pool.query(ownedSharesQuery, [userId]);

        // Get shares where user is the recipient
        const receivedSharesQuery = `
            SELECT 
                s.*,
                u.email as owner_email,
                a.name as account_name,
                a.type as account_type
            FROM account_shares s
            JOIN users u ON s.owner_id = u.id
            JOIN accounts a ON s.account_id = a.id
            WHERE s.shared_with_id = $1
            ORDER BY s.shared_at DESC
        `;

        const receivedShares = await pool.query(receivedSharesQuery, [userId]);

        res.json({
            owned_shares: ownedShares.rows,
            received_shares: receivedShares.rows,
            summary: {
                accounts_shared_by_me: ownedShares.rows.length,
                accounts_shared_with_me: receivedShares.rows.length
            }
        });
    } catch (error) {
        next(error);
    }
});

// Create account share (Premium feature)
router.post('/account-shares', authenticateToken, requirePremium('account_sharing'), [
    body('account_id')
        .isUUID()
        .withMessage('Account ID must be a valid UUID'),
    body('share_with_email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Must be a valid email address'),
    body('permission_level')
        .isIn(['view', 'edit'])
        .withMessage('Permission level must be view or edit'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const { account_id, share_with_email, permission_level } = req.body;

        // Verify account belongs to user
        const accountCheck = await pool.query(
            'SELECT * FROM accounts WHERE id = $1 AND user_id = $2',
            [account_id, userId]
        );

        if (accountCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Find user to share with
        const shareWithUser = await pool.query(
            'SELECT id, email FROM users WHERE email = $1',
            [share_with_email]
        );

        if (shareWithUser.rows.length === 0) {
            return res.status(404).json({ error: 'User not found with that email address' });
        }

        const shareWithUserId = shareWithUser.rows[0].id;

        // Prevent sharing with self
        if (shareWithUserId === userId) {
            return res.status(400).json({ error: 'Cannot share account with yourself' });
        }

        // Check if share already exists
        const existingShare = await pool.query(
            'SELECT id FROM account_shares WHERE owner_id = $1 AND shared_with_id = $2 AND account_id = $3',
            [userId, shareWithUserId, account_id]
        );

        if (existingShare.rows.length > 0) {
            return res.status(409).json({ error: 'Account is already shared with this user' });
        }

        // Create share
        const result = await pool.query(`
            INSERT INTO account_shares (owner_id, shared_with_id, account_id, permission_level)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        `, [userId, shareWithUserId, account_id, permission_level]);

        const share = result.rows[0];

        // TODO: Send notification email to shared user
        // await sendShareNotification(share_with_email, req.user.email, accountCheck.rows[0].name);

        logger.info(`Account shared: ${account_id} with ${share_with_email} by user: ${userId}`);

        res.status(201).json({
            message: 'Account shared successfully',
            share: {
                ...share,
                shared_with_email: share_with_email,
                account_name: accountCheck.rows[0].name
            }
        });
    } catch (error) {
        next(error);
    }
});

// Update account share permissions (Premium feature)
router.put('/account-shares/:id', authenticateToken, requirePremium('account_sharing'), [
    body('permission_level')
        .isIn(['view', 'edit'])
        .withMessage('Permission level must be view or edit'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const shareId = req.params.id;
        const { permission_level } = req.body;

        // Update share (only owner can update)
        const result = await pool.query(`
            UPDATE account_shares 
            SET permission_level = $3
            WHERE id = $1 AND owner_id = $2
            RETURNING *
        `, [shareId, userId, permission_level]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Account share not found' });
        }

        const share = result.rows[0];

        logger.info(`Account share updated: ${shareId} by user: ${userId}`);

        res.json({
            message: 'Account share updated successfully',
            share
        });
    } catch (error) {
        next(error);
    }
});

// Delete account share (Premium feature)
router.delete('/account-shares/:id', authenticateToken, requirePremium('account_sharing'), async (req, res, next) => {
    try {
        const userId = req.userId;
        const shareId = req.params.id;

        // Delete share (owner or recipient can delete)
        const result = await pool.query(
            'DELETE FROM account_shares WHERE id = $1 AND (owner_id = $2 OR shared_with_id = $2) RETURNING *',
            [shareId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Account share not found' });
        }

        logger.info(`Account share deleted: ${shareId} by user: ${userId}`);

        res.json({ message: 'Account share removed successfully' });
    } catch (error) {
        next(error);
    }
});

// Get shared account details (Premium feature)
router.get('/shared-accounts/:id', authenticateToken, requirePremium('account_sharing'), async (req, res, next) => {
    try {
        const userId = req.userId;
        const accountId = req.params.id;

        // Check if user has access to this account (owner or shared with)
        const accessCheck = await pool.query(`
            SELECT 
                a.*,
                s.permission_level,
                s.owner_id,
                u.email as owner_email,
                CASE WHEN a.user_id = $1 THEN 'owner' ELSE 'shared' END as access_type
            FROM accounts a
            LEFT JOIN account_shares s ON s.account_id = a.id AND s.shared_with_id = $1
            LEFT JOIN users u ON s.owner_id = u.id
            WHERE a.id = $2 AND (a.user_id = $1 OR s.shared_with_id = $1)
        `, [userId, accountId]);

        if (accessCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found or access denied' });
        }

        const account = accessCheck.rows[0];
        account.balance = parseFloat(account.balance);

        // Get recent transactions (limited for shared accounts)
        const transactionLimit = account.access_type === 'owner' ? 50 : 20;
        const transactionsQuery = `
            SELECT 
                t.*,
                c.name as category_name
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.account_id = $1
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT $2
        `;

        const transactions = await pool.query(transactionsQuery, [accountId, transactionLimit]);

        res.json({
            account: {
                ...account,
                can_edit: account.access_type === 'owner' || account.permission_level === 'edit'
            },
            recent_transactions: transactions.rows,
            access_info: {
                type: account.access_type,
                permission_level: account.permission_level,
                owner_email: account.owner_email
            }
        });
    } catch (error) {
        next(error);
    }
});

// Get premium analytics dashboard (Premium feature)
router.get('/analytics-dashboard', authenticateToken, requirePremium('advanced_analytics'), async (req, res, next) => {
    try {
        const userId = req.userId;

        // Get comprehensive analytics data
        const [
            spendingTrends,
            categoryBreakdown,
            predictiveAlerts
        ] = await Promise.all([
            analyticsService.getSpendingTrends(userId, 12), // 12 months
            analyticsService.getCategoryBreakdown(
                userId,
                new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Start of current month
                new Date() // Today
            ),
            analyticsService.getPredictiveAlerts(userId)
        ]);

        // Get account performance
        const accountPerformanceQuery = `
            SELECT 
                a.id,
                a.name,
                a.type,
                a.balance,
                COUNT(t.id) as transaction_count,
                SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END) as total_income,
                SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as total_expenses
            FROM accounts a
            LEFT JOIN transactions t ON t.account_id = a.id 
                AND t.transaction_date >= CURRENT_DATE - INTERVAL '30 days'
            WHERE a.user_id = $1 AND a.is_active = true
            GROUP BY a.id, a.name, a.type, a.balance
            ORDER BY a.balance DESC
        `;

        const accountPerformance = await pool.query(accountPerformanceQuery, [userId]);

        // Get budget performance
        const budgetPerformanceQuery = `
            SELECT 
                b.*,
                c.name as category_name,
                COALESCE(SUM(t.amount), 0) as spent_amount
            FROM budgets b
            LEFT JOIN categories c ON b.category_id = c.id
            LEFT JOIN transactions t ON t.category_id = c.id 
                AND t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date BETWEEN b.start_date AND b.end_date
            WHERE b.user_id = $1 AND b.is_active = true
            GROUP BY b.id, c.name
            ORDER BY b.created_at DESC
        `;

        const budgetPerformance = await pool.query(budgetPerformanceQuery, [userId]);

        res.json({
            spending_trends: spendingTrends.map(trend => ({
                month: trend.month,
                expenses: parseFloat(trend.expenses || 0),
                income: parseFloat(trend.income || 0),
                net: parseFloat(trend.income || 0) - parseFloat(trend.expenses || 0),
                transaction_count: parseInt(trend.transaction_count)
            })),
            category_breakdown: categoryBreakdown.map(category => ({
                name: category.category_name,
                color: category.color,
                total_amount: parseFloat(category.total_amount),
                transaction_count: parseInt(category.transaction_count),
                avg_amount: parseFloat(category.avg_amount)
            })),
            predictive_alerts: predictiveAlerts,
            account_performance: accountPerformance.rows.map(account => ({
                ...account,
                balance: parseFloat(account.balance),
                transaction_count: parseInt(account.transaction_count),
                total_income: parseFloat(account.total_income || 0),
                total_expenses: parseFloat(account.total_expenses || 0),
                net_flow: parseFloat(account.total_income || 0) - parseFloat(account.total_expenses || 0)
            })),
            budget_performance: budgetPerformance.rows.map(budget => {
                const spentAmount = parseFloat(budget.spent_amount);
                const budgetAmount = parseFloat(budget.amount);
                const percentage = budgetAmount > 0 ? ((spentAmount / budgetAmount) * 100) : 0;
                
                return {
                    ...budget,
                    amount: budgetAmount,
                    spent_amount: spentAmount,
                    remaining_amount: budgetAmount - spentAmount,
                    percentage_used: percentage,
                    status: percentage >= 100 ? 'over_budget' : 
                           percentage >= 80 ? 'approaching_limit' : 'on_track'
                };
            }),
            generated_at: new Date().toISOString()
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;