const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body } = require('express-validator');
const { pool } = require('../config/database');
const { handleValidationErrors } = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

// Registration
router.post('/register', [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const { email, password } = req.body;

        // Check if user already exists
        const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
        if (existingUser.rows.length > 0) {
            return res.status(409).json({ error: 'User already exists' });
        }

        // Hash password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Create user
        const result = await pool.query(
            'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id, email, subscription_tier, created_at',
            [email, hashedPassword]
        );

        const user = result.rows[0];

        // Generate tokens
        const accessToken = jwt.sign(
            { userId: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '15m' }
        );

        const refreshToken = jwt.sign(
            { userId: user.id },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: '7d' }
        );

        // Store refresh token
        await pool.query(
            'UPDATE users SET refresh_token = $1 WHERE id = $2',
            [refreshToken, user.id]
        );

        // Create default categories
        await createDefaultCategories(user.id);

        // Create default notification preferences (simplified for now)
        try {
            await pool.query(`
                INSERT INTO notification_preferences (
                    user_id, push_notifications, in_app_notifications, email_notifications,
                    frequency, quiet_hours_start, quiet_hours_end, goal_reminders,
                    budget_alerts, insight_notifications, promotional_notifications
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                ON CONFLICT (user_id) DO NOTHING
            `, [
                user.id,
                true, // push_notifications
                true, // in_app_notifications
                true, // email_notifications
                'smart', // frequency
                '22:00', // quiet_hours_start
                '08:00', // quiet_hours_end
                true, // goal_reminders
                true, // budget_alerts
                true, // insight_notifications
                false // promotional_notifications
            ]);
        } catch (prefError) {
            logger.warn('Failed to create notification preferences:', prefError);
            // Continue without failing registration
        }

        logger.info(`User registered: ${email}`);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: {
                id: user.id,
                email: user.email,
                subscription_tier: user.subscription_tier
            },
            tokens: {
                access_token: accessToken,
                refresh_token: refreshToken,
                token_type: 'Bearer',
                expires_in: 900
            }
        });
    } catch (error) {
        next(error);
    }
});

// Login - FIXED VERSION
router.post('/login', [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const { email, password } = req.body;

        logger.info(`Login attempt for email: ${email}`);

        // Find user
        const result = await pool.query(
            'SELECT id, email, password_hash, subscription_tier, subscription_expires_at FROM users WHERE email = $1',
            [email]
        );

        if (result.rows.length === 0) {
            logger.warn(`Login failed: User not found for email: ${email}`);
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = result.rows[0];

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        if (!isPasswordValid) {
            logger.warn(`Login failed: Invalid password for email: ${email}`);
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Validate JWT secrets exist
        if (!process.env.JWT_SECRET || !process.env.JWT_REFRESH_SECRET) {
            logger.error('JWT secrets not configured');
            return res.status(500).json({ error: 'Server configuration error' });
        }

        // Generate tokens with proper error handling
        let accessToken, refreshToken;
        
        try {
            accessToken = jwt.sign(
                { 
                    userId: user.id, 
                    email: user.email,
                    subscription_tier: user.subscription_tier 
                },
                process.env.JWT_SECRET,
                { 
                    expiresIn: '15m',
                    issuer: 'finance-manager-api',
                    audience: 'finance-manager-app'
                }
            );

            refreshToken = jwt.sign(
                { 
                    userId: user.id,
                    type: 'refresh'
                },
                process.env.JWT_REFRESH_SECRET,
                { 
                    expiresIn: '7d',
                    issuer: 'finance-manager-api',
                    audience: 'finance-manager-app'
                }
            );
        } catch (tokenError) {
            logger.error('Token generation failed:', tokenError);
            return res.status(500).json({ error: 'Token generation failed' });
        }

        // Store refresh token in database
        try {
            await pool.query(
                'UPDATE users SET refresh_token = $1 WHERE id = $2',
                [refreshToken, user.id]
            );
        } catch (dbError) {
            logger.error('Failed to store refresh token:', dbError);
            return res.status(500).json({ error: 'Failed to store session' });
        }

        logger.info(`User logged in successfully: ${email}`);

        // Return consistent response structure
        res.status(200).json({
            success: true,
            message: 'Login successful',
            user: {
                id: user.id,
                email: user.email,
                subscription_tier: user.subscription_tier,
                subscription_expires_at: user.subscription_expires_at
            },
            tokens: {
                access_token: accessToken,
                refresh_token: refreshToken,
                token_type: 'Bearer',
                expires_in: 900 // 15 minutes in seconds
            }
        });
    } catch (error) {
        logger.error('Login error:', error);
        next(error);
    }
});

// Refresh token - ENHANCED VERSION
router.post('/refresh', async (req, res, next) => {
    try {
        const { refresh_token } = req.body;

        if (!refresh_token) {
            logger.warn('Refresh attempt without token');
            return res.status(401).json({ error: 'Refresh token required' });
        }

        // Validate JWT secrets
        if (!process.env.JWT_REFRESH_SECRET || !process.env.JWT_SECRET) {
            logger.error('JWT secrets not configured for refresh');
            return res.status(500).json({ error: 'Server configuration error' });
        }

        let decoded;
        try {
            decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET, {
                issuer: 'finance-manager-api',
                audience: 'finance-manager-app'
            });
        } catch (jwtError) {
            logger.warn('Invalid refresh token:', jwtError.message);
            return res.status(401).json({ error: 'Invalid refresh token' });
        }
        
        // Verify refresh token in database
        const result = await pool.query(
            'SELECT id, email, subscription_tier, refresh_token FROM users WHERE id = $1',
            [decoded.userId]
        );

        if (result.rows.length === 0) {
            logger.warn(`Refresh failed: User not found for ID: ${decoded.userId}`);
            return res.status(401).json({ error: 'User not found' });
        }

        const user = result.rows[0];

        if (user.refresh_token !== refresh_token) {
            logger.warn(`Refresh failed: Token mismatch for user: ${user.email}`);
            return res.status(401).json({ error: 'Invalid refresh token' });
        }

        // Generate new access token
        let accessToken;
        try {
            accessToken = jwt.sign(
                { 
                    userId: user.id, 
                    email: user.email,
                    subscription_tier: user.subscription_tier 
                },
                process.env.JWT_SECRET,
                { 
                    expiresIn: '15m',
                    issuer: 'finance-manager-api',
                    audience: 'finance-manager-app'
                }
            );
        } catch (tokenError) {
            logger.error('Access token generation failed:', tokenError);
            return res.status(500).json({ error: 'Token generation failed' });
        }

        logger.info(`Token refreshed for user: ${user.email}`);

        res.status(200).json({
            success: true,
            access_token: accessToken,
            token_type: 'Bearer',
            expires_in: 900 // 15 minutes in seconds
        });
    } catch (error) {
        logger.error('Refresh token error:', error);
        next(error);
    }
});

// Logout - ENHANCED VERSION
router.post('/logout', async (req, res, next) => {
    try {
        const { refresh_token } = req.body;

        if (refresh_token) {
            try {
                const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);
                await pool.query(
                    'UPDATE users SET refresh_token = NULL WHERE id = $1',
                    [decoded.userId]
                );
                logger.info(`User logged out: ${decoded.userId}`);
            } catch (error) {
                logger.warn('Logout token verification failed:', error.message);
                // Continue with logout even if token verification fails
            }
        }

        res.status(200).json({ 
            success: true,
            message: 'Logged out successfully' 
        });
    } catch (error) {
        logger.error('Logout error:', error);
        // Even if logout fails, return success to client
        res.status(200).json({ 
            success: true,
            message: 'Logged out successfully' 
        });
    }
});

// Test endpoint for debugging authentication
router.get('/test', async (req, res) => {
    try {
        // Test database connection
        const dbTest = await pool.query('SELECT NOW() as current_time');
        
        // Test JWT configuration
        const jwtTest = {
            jwt_secret_configured: !!process.env.JWT_SECRET,
            jwt_refresh_secret_configured: !!process.env.JWT_REFRESH_SECRET,
            jwt_secret_length: process.env.JWT_SECRET ? process.env.JWT_SECRET.length : 0,
            jwt_refresh_secret_length: process.env.JWT_REFRESH_SECRET ? process.env.JWT_REFRESH_SECRET.length : 0
        };

        // Test user count
        const userCount = await pool.query('SELECT COUNT(*) as count FROM users');

        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            database: {
                connected: true,
                current_time: dbTest.rows[0].current_time
            },
            jwt_config: jwtTest,
            users: {
                total_count: parseInt(userCount.rows[0].count)
            },
            environment: {
                node_env: process.env.NODE_ENV || 'development',
                port: process.env.PORT || 3000
            }
        });
    } catch (error) {
        logger.error('Auth test failed:', error);
        res.status(500).json({
            status: 'ERROR',
            error: error.message
        });
    }
});

// Helper function to create default categories
async function createDefaultCategories(userId) {
    const defaultCategories = [
        { name: 'Food & Dining', color: '#FF6B6B', icon: 'utensils' },
        { name: 'Transportation', color: '#4ECDC4', icon: 'car' },
        { name: 'Shopping', color: '#45B7D1', icon: 'shopping-bag' },
        { name: 'Entertainment', color: '#96CEB4', icon: 'film' },
        { name: 'Bills & Utilities', color: '#FFEAA7', icon: 'file-text' },
        { name: 'Healthcare', color: '#DDA0DD', icon: 'heart' },
        { name: 'Income', color: '#98D8C8', icon: 'dollar-sign' },
        { name: 'Savings', color: '#F7DC6F', icon: 'piggy-bank' }
    ];

    for (const category of defaultCategories) {
        try {
            await pool.query(
                'INSERT INTO categories (user_id, name, color, icon, is_default) VALUES ($1, $2, $3, $4, true)',
                [userId, category.name, category.color, category.icon]
            );
        } catch (error) {
            logger.warn(`Failed to create category ${category.name}:`, error);
            // Continue with other categories
        }
    }
}

module.exports = router;