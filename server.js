const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { connectDB } = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');

// Route imports
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const accountRoutes = require('./routes/accounts');
const transactionRoutes = require('./routes/transactions');
const categoryRoutes = require('./routes/categories');
const budgetRoutes = require('./routes/budgets');
const billRoutes = require('./routes/bills');
const analyticsRoutes = require('./routes/analytics');
const premiumRoutes = require('./routes/premium');
const statementRoutes = require('./routes/statements');
const goalRoutes = require('./routes/goals');
const recommendationRoutes = require('./routes/recommendations');
const notificationRoutes = require('./routes/notifications');
const insightRoutes = require('./routes/insights');
const healthRoutes = require('./routes/health'); // NEW: Health check routes

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: 'Too many requests', code: 'RATE_LIMITED' },
  skip: () => process.env.NODE_ENV !== 'production' || process.env.DISABLE_RATE_LIMIT === 'true'
});

// Same for uploadLimiter, aiLimiter, recommendationLimiter
app.use('/api/', limiter);

// Special rate limiting for file uploads
const uploadLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20, // limit each IP to 20 uploads per hour
    message: 'Too many file uploads from this IP, please try again later.'
});
app.use('/api/statements/upload', uploadLimiter);

// Special rate limiting for AI endpoints
const aiLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 50, // limit each IP to 50 AI requests per hour
    message: 'Too many AI requests from this IP, please try again later.'
});
app.use('/api/goals/ai', aiLimiter);

// Rate limiting for recommendation generation
const recommendationLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // limit each IP to 10 recommendation generations per hour
    message: 'Too many recommendation requests from this IP, please try again later.'
});
app.use('/api/recommendations/generate', recommendationLimiter);

// Middleware
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) }}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check routes (before authentication)
app.use('/health', healthRoutes);

// Legacy health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/budgets', budgetRoutes);
app.use('/api/bills', billRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/premium', premiumRoutes);
app.use('/api/statements', statementRoutes);
app.use('/api/goals', goalRoutes);
app.use('/api/recommendations', recommendationRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/insights', insightRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Start server
const startServer = async () => {
    try {
        await connectDB();
        await connectRedis();
        
        // Start background workers
        require('./workers/statementProcessor');
        require('./workers/aiCategorizer');
        require('./workers/transactionImporter');
        
        // Start recommendation generator
        const recommendationGenerator = require('./workers/recommendationGenerator');
        recommendationGenerator.start();
        
        app.listen(PORT, () => {
            logger.info(`Server running on port ${PORT}`);
            logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
            logger.info('Background workers started');
            logger.info('AI-powered goal setting feature enabled');
            logger.info('AI-powered recommendations and notifications system enabled');
            logger.info('Enhanced database connection pool and health monitoring enabled');
        });
    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
};

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    
    // Stop recommendation generator
    const recommendationGenerator = require('./workers/recommendationGenerator');
    recommendationGenerator.stop();
    
    process.exit(0);
});

startServer();

module.exports = app;