const logger = require('../utils/logger');

// Mock Queue implementation for development (when Redis is not available)
class MockQueue {
    constructor(name, options = {}) {
        this.name = name;
        this.options = options;
        this.jobs = [];
        this.processors = new Map();
        this.isProcessing = false;
        
        logger.info(`Mock Queue created: ${name}`);
    }

    // Add job to queue
    async add(jobType, data, options = {}) {
        const job = {
            id: Date.now() + Math.random(),
            type: jobType,
            data: data,
            options: options,
            createdAt: new Date(),
            status: 'waiting'
        };
        
        this.jobs.push(job);
        logger.info(`Job added to mock queue ${this.name}: ${jobType}`);
        
        // Process immediately in development
        setImmediate(() => this.processJobs());
        
        return job;
    }

    // Register job processor
    process(jobType, processor) {
        this.processors.set(jobType, processor);
        logger.info(`Processor registered for ${this.name}: ${jobType}`);
    }

    // Process jobs
    async processJobs() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        
        try {
            while (this.jobs.length > 0) {
                const job = this.jobs.shift();
                const processor = this.processors.get(job.type);
                
                if (processor) {
                    try {
                        job.status = 'processing';
                        logger.info(`Processing mock job: ${job.type} in queue ${this.name}`);
                        
                        const result = await processor(job);
                        job.status = 'completed';
                        job.result = result;
                        
                        logger.info(`Mock job completed: ${job.type}`);
                        this.emit('completed', job, result);
                    } catch (error) {
                        job.status = 'failed';
                        job.error = error;
                        
                        logger.error(`Mock job failed: ${job.type}`, error);
                        this.emit('failed', job, error);
                    }
                } else {
                    logger.warn(`No processor found for job type: ${job.type}`);
                    job.status = 'failed';
                    job.error = new Error(`No processor for job type: ${job.type}`);
                }
            }
        } finally {
            this.isProcessing = false;
        }
    }

    // Event emitter methods
    on(event, callback) {
        if (!this.listeners) this.listeners = {};
        if (!this.listeners[event]) this.listeners[event] = [];
        this.listeners[event].push(callback);
    }

    emit(event, ...args) {
        if (this.listeners && this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    logger.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    // Close queue
    async close() {
        logger.info(`Mock queue ${this.name} closed`);
    }
}

// Try to use real Bull queues, fall back to mock if Redis is not available
let useRealQueues = false;

try {
    // Check if we can use real Redis/Bull
    const { getRedisClient } = require('./redis');
    const redisClient = getRedisClient();
    
    if (redisClient && typeof redisClient.ping === 'function') {
        // Try to use real Bull queues
        try {
            const Queue = require('bull');
            
            const redisConfig = {
                host: process.env.REDIS_HOST || 'localhost',
                port: process.env.REDIS_PORT || 6379,
                password: process.env.REDIS_PASSWORD,
                maxRetriesPerRequest: 3,
                retryDelayOnFailover: 100,
                enableReadyCheck: false,
                maxRetriesPerRequest: null
            };

            // Create queues with real Bull
            const statementProcessingQueue = new Queue('statement processing', {
                redis: redisConfig,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000
                    }
                }
            });

            const aiCategorizationQueue = new Queue('ai categorization', {
                redis: redisConfig,
                defaultJobOptions: {
                    removeOnComplete: 5,
                    removeOnFail: 25,
                    attempts: 2,
                    backoff: {
                        type: 'exponential',
                        delay: 5000
                    }
                }
            });

            const importQueue = new Queue('transaction import', {
                redis: redisConfig,
                defaultJobOptions: {
                    removeOnComplete: 5,
                    removeOnFail: 25,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 1000
                    }
                }
            });

            // Set up event handlers
            statementProcessingQueue.on('completed', (job, result) => {
                logger.info(`Statement processing job ${job.id} completed`);
            });

            statementProcessingQueue.on('failed', (job, err) => {
                logger.error(`Statement processing job ${job.id} failed:`, err);
            });

            aiCategorizationQueue.on('completed', (job, result) => {
                logger.info(`AI categorization job ${job.id} completed`);
            });

            aiCategorizationQueue.on('failed', (job, err) => {
                logger.error(`AI categorization job ${job.id} failed:`, err);
            });

            importQueue.on('completed', (job, result) => {
                logger.info(`Import job ${job.id} completed`);
            });

            importQueue.on('failed', (job, err) => {
                logger.error(`Import job ${job.id} failed:`, err);
            });

            useRealQueues = true;
            logger.info('Using real Bull queues with Redis');

            module.exports = {
                statementProcessingQueue,
                aiCategorizationQueue,
                importQueue
            };

        } catch (bullError) {
            logger.warn('Failed to initialize Bull queues, using mock queues:', bullError.message);
            useRealQueues = false;
        }
    }
} catch (error) {
    logger.warn('Redis not available, using mock queues:', error.message);
    useRealQueues = false;
}

// Fall back to mock queues if real ones couldn't be initialized
if (!useRealQueues) {
    logger.info('Using mock queue implementation for development');

    const statementProcessingQueue = new MockQueue('statement processing');
    const aiCategorizationQueue = new MockQueue('ai categorization');
    const importQueue = new MockQueue('transaction import');

    // Graceful shutdown for mock queues
    process.on('SIGTERM', async () => {
        logger.info('Closing mock queues...');
        await statementProcessingQueue.close();
        await aiCategorizationQueue.close();
        await importQueue.close();
    });

    module.exports = {
        statementProcessingQueue,
        aiCategorizationQueue,
        importQueue
    };
}