const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { validateBudget } = require('../middleware/validation');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Get all budgets for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { active_only = 'true' } = req.query;

        let query = `
            SELECT 
                b.*,
                c.name as category_name,
                c.color as category_color,
                COALESCE(SUM(t.amount), 0) as spent_amount
            FROM budgets b
            LEFT JOIN categories c ON b.category_id = c.id
            LEFT JOIN transactions t ON t.category_id = c.id 
                AND t.user_id = $1 
                AND t.type = 'expense'
                AND t.transaction_date BETWEEN b.start_date AND b.end_date
            WHERE b.user_id = $1
        `;

        const params = [userId];

        if (active_only === 'true') {
            query += ' AND b.is_active = true';
        }

        query += `
            GROUP BY b.id, c.name, c.color
            ORDER BY b.created_at DESC
        `;

        const result = await pool.query(query, params);
        
        const budgets = result.rows.map(budget => {
            const spentAmount = parseFloat(budget.spent_amount);
            const budgetAmount = parseFloat(budget.amount);
            const percentage = budgetAmount > 0 ? ((spentAmount / budgetAmount) * 100) : 0;
            
            return {
                ...budget,
                amount: budgetAmount,
                spent_amount: spentAmount,
                remaining_amount: budgetAmount - spentAmount,
                percentage_used: percentage,
                status: getbudgetStatus(percentage, budget.alert_threshold),
                alert_threshold: parseFloat(budget.alert_threshold)
            };
        });

        res.json({ budgets });
    } catch (error) {
        next(error);
    }
});

// Get single budget with detailed spending
router.get('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const budgetId = req.params.id;

        // Get budget details
        const budgetQuery = `
            SELECT 
                b.*,
                c.name as category_name,
                c.color as category_color
            FROM budgets b
            LEFT JOIN categories c ON b.category_id = c.id
            WHERE b.id = $1 AND b.user_id = $2
        `;

        const budgetResult = await pool.query(budgetQuery, [budgetId, userId]);

        if (budgetResult.rows.length === 0) {
            return res.status(404).json({ error: 'Budget not found' });
        }

        const budget = budgetResult.rows[0];

        // Get spending details
        const spendingQuery = `
            SELECT 
                t.*,
                a.name as account_name
            FROM transactions t
            LEFT JOIN accounts a ON t.account_id = a.id
            WHERE t.category_id = $1 
            AND t.user_id = $2 
            AND t.type = 'expense'
            AND t.transaction_date BETWEEN $3 AND $4
            ORDER BY t.transaction_date DESC
        `;

        const spendingResult = await pool.query(spendingQuery, [
            budget.category_id,
            userId,
            budget.start_date,
            budget.end_date
        ]);

        const transactions = spendingResult.rows;
        const spentAmount = transactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
        const budgetAmount = parseFloat(budget.amount);
        const percentage = budgetAmount > 0 ? ((spentAmount / budgetAmount) * 100) : 0;

        // Get daily spending breakdown
        const dailySpending = await getDailySpending(
            userId,
            budget.category_id,
            budget.start_date,
            budget.end_date
        );

        res.json({
            budget: {
                ...budget,
                amount: budgetAmount,
                spent_amount: spentAmount,
                remaining_amount: budgetAmount - spentAmount,
                percentage_used: percentage,
                status: getbudgetStatus(percentage, budget.alert_threshold),
                alert_threshold: parseFloat(budget.alert_threshold)
            },
            transactions,
            daily_spending: dailySpending
        });
    } catch (error) {
        next(error);
    }
});

// Create budget
router.post('/', authenticateToken, validateBudget, async (req, res, next) => {
    try {
        const userId = req.userId;
        const {
            category_id,
            amount,
            period,
            start_date,
            end_date,
            alert_threshold = 0.8
        } = req.body;

        // Verify category belongs to user
        const categoryCheck = await pool.query(
            'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
            [category_id, userId]
        );

        if (categoryCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Category not found' });
        }

        // Check for overlapping budgets for the same category
        const overlapCheck = await pool.query(`
            SELECT id FROM budgets 
            WHERE user_id = $1 
            AND category_id = $2 
            AND is_active = true
            AND (
                (start_date <= $3 AND end_date >= $3) OR
                (start_date <= $4 AND end_date >= $4) OR
                (start_date >= $3 AND end_date <= $4)
            )
        `, [userId, category_id, start_date, end_date]);

        if (overlapCheck.rows.length > 0) {
            return res.status(409).json({ 
                error: 'A budget already exists for this category in the specified period' 
            });
        }

        // Create budget
        const result = await pool.query(`
            INSERT INTO budgets (
                user_id, category_id, amount, period, start_date, end_date, alert_threshold
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `, [userId, category_id, amount, period, start_date, end_date, alert_threshold]);

        const budget = result.rows[0];

        logger.info(`Budget created: ${budget.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Budget created successfully',
            budget: {
                ...budget,
                amount: parseFloat(budget.amount),
                alert_threshold: parseFloat(budget.alert_threshold)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Update budget
router.put('/:id', authenticateToken, validateBudget, async (req, res, next) => {
    try {
        const userId = req.userId;
        const budgetId = req.params.id;
        const {
            category_id,
            amount,
            period,
            start_date,
            end_date,
            alert_threshold,
            is_active
        } = req.body;

        // Verify budget exists and belongs to user
        const budgetCheck = await pool.query(
            'SELECT * FROM budgets WHERE id = $1 AND user_id = $2',
            [budgetId, userId]
        );

        if (budgetCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Budget not found' });
        }

        // Verify category belongs to user
        const categoryCheck = await pool.query(
            'SELECT id FROM categories WHERE id = $1 AND user_id = $2',
            [category_id, userId]
        );

        if (categoryCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Category not found' });
        }

        // Check for overlapping budgets (excluding current budget)
        const overlapCheck = await pool.query(`
            SELECT id FROM budgets 
            WHERE user_id = $1 
            AND category_id = $2 
            AND id != $3
            AND is_active = true
            AND (
                (start_date <= $4 AND end_date >= $4) OR
                (start_date <= $5 AND end_date >= $5) OR
                (start_date >= $4 AND end_date <= $5)
            )
        `, [userId, category_id, budgetId, start_date, end_date]);

        if (overlapCheck.rows.length > 0) {
            return res.status(409).json({ 
                error: 'A budget already exists for this category in the specified period' 
            });
        }

        // Update budget
        const result = await pool.query(`
            UPDATE budgets SET
                category_id = $3,
                amount = $4,
                period = $5,
                start_date = $6,
                end_date = $7,
                alert_threshold = $8,
                is_active = COALESCE($9, is_active)
            WHERE id = $1 AND user_id = $2
            RETURNING *
        `, [
            budgetId, userId, category_id, amount, period, 
            start_date, end_date, alert_threshold, is_active
        ]);

        const budget = result.rows[0];

        logger.info(`Budget updated: ${budgetId} for user: ${userId}`);

        res.json({
            message: 'Budget updated successfully',
            budget: {
                ...budget,
                amount: parseFloat(budget.amount),
                alert_threshold: parseFloat(budget.alert_threshold)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Delete budget
router.delete('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const budgetId = req.params.id;

        const result = await pool.query(
            'DELETE FROM budgets WHERE id = $1 AND user_id = $2 RETURNING *',
            [budgetId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Budget not found' });
        }

        logger.info(`Budget deleted: ${budgetId} for user: ${userId}`);

        res.json({ message: 'Budget deleted successfully' });
    } catch (error) {
        next(error);
    }
});

// Helper function to get budget status
function getbudgetStatus(percentage, alertThreshold) {
    const threshold = alertThreshold * 100;
    
    if (percentage >= 100) {
        return 'over_budget';
    } else if (percentage >= threshold) {
        return 'approaching_limit';
    } else if (percentage >= threshold * 0.5) {
        return 'on_track';
    } else {
        return 'under_budget';
    }
}

// Helper function to get daily spending breakdown
async function getDailySpending(userId, categoryId, startDate, endDate) {
    const query = `
        SELECT 
            DATE(transaction_date) as date,
            SUM(amount) as amount
        FROM transactions
        WHERE user_id = $1 
        AND category_id = $2 
        AND type = 'expense'
        AND transaction_date BETWEEN $3 AND $4
        GROUP BY DATE(transaction_date)
        ORDER BY date ASC
    `;

    const result = await pool.query(query, [userId, categoryId, startDate, endDate]);
    
    return result.rows.map(row => ({
        date: row.date,
        amount: parseFloat(row.amount)
    }));
}

module.exports = router;