require('dotenv').config(); // Load environment variables first
const { connectDB, runMigrations, gracefulShutdown } = require('../config/database');
const logger = require('../utils/logger');

async function migrate() {
    try {
        logger.info('Starting database migration...');
        
        // Use connectDB which includes retry logic and proper error handling
        await connectDB();
        logger.info('Database connection established for migration');
        
        // Run migrations
        await runMigrations();
        logger.info('Database migration completed successfully');
        
        // Gracefully close connections
        await gracefulShutdown();
        process.exit(0);
    } catch (error) {
        logger.error('Migration failed:', error);
        
        // Ensure connections are closed even on error
        try {
            await gracefulShutdown();
        } catch (shutdownError) {
            logger.error('Error during shutdown:', shutdownError);
        }
        
        process.exit(1);
    }
}

migrate();