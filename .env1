# Database Configuration
DB_HOST=finance567.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=postgres
DB_USER=vishwavikas
DB_PASSWORD=vishwa555

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6
JWT_REFRESH_SECRET=z6y5x4w3v2u1t0s9r8q7p6o5n4m3l2k1j0i9h8g7f6e5d4c3b2a1

# API Keys
EXCHANGE_RATE_API_KEY=5e66cb38c69609aa9e3ce33c
COHERE_API_KEY=aKPbm8ElEl3qSA8FHQ8sHPzObmo1kcH0S4oeTOWK

# AWS Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=w12V/FepnHajuuax4WQuvb/5G5XEkzCX4l9Bpuxz
AWS_REGION=eu-north-1
AWS_S3_BUCKET=finance-manager-uploads

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://***************:3000
npm run db:migrate
npm run db:seed
# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging
LOG_LEVEL=info