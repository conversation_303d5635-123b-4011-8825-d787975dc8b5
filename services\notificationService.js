const { pool } = require('../config/database');
const logger = require('../utils/logger');
const nodemailer = require('nodemailer');

class NotificationService {
    constructor() {
        this.emailTransporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: process.env.SMTP_PORT || 587,
            secure: false,
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });

        this.notificationTiming = {
            MORNING_INSIGHTS: {
                time: '09:00',
                types: ['daily_summary', 'goal_progress'],
                frequency: 'daily'
            },
            EVENING_REMINDERS: {
                time: '19:00',
                types: ['bill_reminders', 'budget_alerts'],
                frequency: 'as_needed'
            },
            WEEKEND_PLANNING: {
                time: 'Saturday 10:00',
                types: ['weekly_insights', 'goal_strategy'],
                frequency: 'weekly'
            },
            MONTH_END_REVIEW: {
                time: 'Last day 18:00',
                types: ['monthly_analysis', 'budget_review'],
                frequency: 'monthly'
            }
        };

        this.pushNotificationTypes = {
            CRITICAL_ALERT: {
                priority: 'high',
                sound: 'urgent',
                examples: ['Budget exceeded by 50%', 'Unusual large transaction']
            },
            GOAL_REMINDER: {
                priority: 'medium',
                sound: 'gentle',
                examples: ['Monthly goal contribution due', 'Goal timeline at risk']
            },
            OPTIMIZATION_TIP: {
                priority: 'low',
                sound: 'default',
                examples: ['Save ₹500 this month', 'Subscription optimization available']
            },
            ACHIEVEMENT: {
                priority: 'medium',
                sound: 'celebration',
                examples: ['Goal milestone reached!', 'New savings record!']
            }
        };
    }

    // Create notification from recommendation
    async createNotificationFromRecommendation(recommendationId, userId, type = 'in_app') {
        try {
            // Get recommendation details
            const recResult = await pool.query(
                'SELECT * FROM ai_recommendations WHERE id = $1 AND user_id = $2',
                [recommendationId, userId]
            );

            if (recResult.rows.length === 0) {
                throw new Error('Recommendation not found');
            }

            const recommendation = recResult.rows[0];

            // Get user notification preferences
            const prefsResult = await pool.query(
                'SELECT * FROM notification_preferences WHERE user_id = $1',
                [userId]
            );

            const preferences = prefsResult.rows[0] || this.getDefaultPreferences();

            // Check if user wants this type of notification
            if (!this.shouldSendNotification(recommendation, preferences, type)) {
                logger.info(`Skipping notification for user ${userId} based on preferences`);
                return null;
            }

            // Create notification message
            const { title, message, actionData } = this.buildNotificationContent(recommendation);

            // Determine delivery channel
            const deliveryChannel = this.getDeliveryChannel(type, preferences);

            // Create notification record
            const notificationResult = await pool.query(`
                INSERT INTO notifications (
                    user_id, recommendation_id, type, title, message, 
                    action_data, delivery_channel, status
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'pending')
                RETURNING *
            `, [
                userId,
                recommendationId,
                type,
                title,
                message,
                JSON.stringify(actionData),
                deliveryChannel
            ]);

            const notification = notificationResult.rows[0];

            // Send notification based on type
            await this.sendNotification(notification, preferences);

            logger.info(`Notification created and sent: ${notification.id} for user: ${userId}`);
            return notification;

        } catch (error) {
            logger.error('Failed to create notification from recommendation:', error);
            throw error;
        }
    }

    // Send notification based on type
    async sendNotification(notification, preferences) {
        try {
            switch (notification.type) {
                case 'push':
                    await this.sendPushNotification(notification);
                    break;
                case 'email':
                    await this.sendEmailNotification(notification, preferences);
                    break;
                case 'in_app':
                    // In-app notifications are just stored in database
                    await this.markNotificationAsSent(notification.id);
                    break;
                default:
                    logger.warn(`Unknown notification type: ${notification.type}`);
            }
        } catch (error) {
            logger.error(`Failed to send notification ${notification.id}:`, error);
            await this.markNotificationAsFailed(notification.id, error.message);
        }
    }

    // Send push notification (placeholder for integration with push service)
    async sendPushNotification(notification) {
        try {
            // TODO: Integrate with Firebase Cloud Messaging or similar service
            // For now, we'll just log and mark as sent
            
            logger.info(`Push notification would be sent: ${notification.title}`);
            
            // Simulate push notification sending
            const pushPayload = {
                title: notification.title,
                body: notification.message,
                data: notification.action_data,
                priority: this.getPushPriority(notification),
                sound: this.getPushSound(notification)
            };

            // In a real implementation, you would send to FCM/APNS here
            // await fcm.send(pushPayload);

            await this.markNotificationAsSent(notification.id);
            
        } catch (error) {
            logger.error('Failed to send push notification:', error);
            throw error;
        }
    }

    // Send email notification
    async sendEmailNotification(notification, preferences) {
        try {
            if (!preferences.email_notifications) {
                logger.info('Email notifications disabled for user');
                return;
            }

            // Get user email
            const userResult = await pool.query(
                'SELECT email FROM users WHERE id = $1',
                [notification.user_id]
            );

            if (userResult.rows.length === 0) {
                throw new Error('User not found');
            }

            const userEmail = userResult.rows[0].email;

            // Build email content
            const emailContent = this.buildEmailContent(notification);

            const mailOptions = {
                from: process.env.SMTP_USER,
                to: userEmail,
                subject: notification.title,
                html: emailContent.html,
                text: emailContent.text
            };

            await this.emailTransporter.sendMail(mailOptions);
            await this.markNotificationAsSent(notification.id);

            logger.info(`Email notification sent to: ${userEmail}`);

        } catch (error) {
            logger.error('Failed to send email notification:', error);
            throw error;
        }
    }

    // Build notification content from recommendation
    buildNotificationContent(recommendation) {
        let title = recommendation.title;
        let message = recommendation.description;
        
        // Add potential savings to message if available
        if (recommendation.potential_savings > 0) {
            message += ` Potential monthly savings: ₹${recommendation.potential_savings.toLocaleString('en-IN')}`;
        }

        const actionData = {
            recommendation_id: recommendation.id,
            type: recommendation.type,
            action: recommendation.action_text,
            deep_link: `/recommendations/${recommendation.id}`
        };

        return { title, message, actionData };
    }

    // Build email content
    buildEmailContent(notification) {
        const actionData = JSON.parse(notification.action_data || '{}');
        
        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2c3e50;">${notification.title}</h2>
                <p style="color: #34495e; line-height: 1.6;">${notification.message}</p>
                
                ${actionData.action ? `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <strong>Recommended Action:</strong> ${actionData.action}
                    </div>
                ` : ''}
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p style="color: #7f8c8d; font-size: 12px;">
                        This is an automated financial insight from your Finance Manager app.
                        <br>
                        To manage your notification preferences, visit your account settings.
                    </p>
                </div>
            </div>
        `;

        const text = `
${notification.title}

${notification.message}

${actionData.action ? `Recommended Action: ${actionData.action}` : ''}

---
This is an automated financial insight from your Finance Manager app.
To manage your notification preferences, visit your account settings.
        `;

        return { html, text };
    }

    // Check if notification should be sent based on preferences
    shouldSendNotification(recommendation, preferences, type) {
        // Check global notification settings
        if (type === 'push' && !preferences.push_notifications) return false;
        if (type === 'email' && !preferences.email_notifications) return false;
        if (type === 'in_app' && !preferences.in_app_notifications) return false;

        // Check specific notification types
        if (recommendation.type === 'goal_focused' && !preferences.goal_reminders) return false;
        if (recommendation.type === 'budget_optimization' && !preferences.budget_alerts) return false;
        if (['behavioral_insight', 'opportunity_alert'].includes(recommendation.type) && !preferences.insight_notifications) return false;

        // Check quiet hours for push notifications
        if (type === 'push' && this.isQuietHours(preferences)) return false;

        return true;
    }

    // Check if current time is within quiet hours
    isQuietHours(preferences) {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const quietStart = this.timeToMinutes(preferences.quiet_hours_start || '22:00');
        const quietEnd = this.timeToMinutes(preferences.quiet_hours_end || '08:00');

        if (quietStart < quietEnd) {
            return currentTime >= quietStart && currentTime <= quietEnd;
        } else {
            // Quiet hours span midnight
            return currentTime >= quietStart || currentTime <= quietEnd;
        }
    }

    // Convert time string to minutes
    timeToMinutes(timeString) {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    }

    // Get delivery channel based on type and preferences
    getDeliveryChannel(type, preferences) {
        switch (type) {
            case 'push':
                return 'mobile_push'; // Could be 'web_push' for web apps
            case 'email':
                return 'email';
            case 'in_app':
                return 'in_app';
            default:
                return 'in_app';
        }
    }

    // Get push notification priority
    getPushPriority(notification) {
        const actionData = JSON.parse(notification.action_data || '{}');
        
        if (actionData.type === 'proactive_alert') return 'high';
        if (actionData.type === 'goal_focused') return 'medium';
        return 'low';
    }

    // Get push notification sound
    getPushSound(notification) {
        const actionData = JSON.parse(notification.action_data || '{}');
        
        if (actionData.type === 'proactive_alert') return 'urgent';
        if (actionData.type === 'goal_focused') return 'gentle';
        return 'default';
    }

    // Mark notification as sent
    async markNotificationAsSent(notificationId) {
        await pool.query(
            'UPDATE notifications SET status = $1, sent_at = NOW() WHERE id = $2',
            ['sent', notificationId]
        );
    }

    // Mark notification as failed
    async markNotificationAsFailed(notificationId, errorMessage) {
        await pool.query(
            'UPDATE notifications SET status = $1, sent_at = NOW() WHERE id = $2',
            ['failed', notificationId]
        );
        
        logger.error(`Notification ${notificationId} failed: ${errorMessage}`);
    }

    // Get default notification preferences
    getDefaultPreferences() {
        return {
            push_notifications: true,
            in_app_notifications: true,
            email_notifications: true,
            frequency: 'smart',
            quiet_hours_start: '22:00',
            quiet_hours_end: '08:00',
            goal_reminders: true,
            budget_alerts: true,
            insight_notifications: true,
            promotional_notifications: false
        };
    }

    // Create notification preferences for new user
    async createDefaultPreferences(userId) {
        try {
            const defaults = this.getDefaultPreferences();
            
            await pool.query(`
                INSERT INTO notification_preferences (
                    user_id, push_notifications, in_app_notifications, email_notifications,
                    frequency, quiet_hours_start, quiet_hours_end, goal_reminders,
                    budget_alerts, insight_notifications, promotional_notifications
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                ON CONFLICT (user_id) DO NOTHING
            `, [
                userId,
                defaults.push_notifications,
                defaults.in_app_notifications,
                defaults.email_notifications,
                defaults.frequency,
                defaults.quiet_hours_start,
                defaults.quiet_hours_end,
                defaults.goal_reminders,
                defaults.budget_alerts,
                defaults.insight_notifications,
                defaults.promotional_notifications
            ]);

            logger.info(`Default notification preferences created for user: ${userId}`);

        } catch (error) {
            logger.error('Failed to create default notification preferences:', error);
            throw error;
        }
    }

    // Send bulk notifications for recommendations
    async sendBulkNotifications(recommendations, type = 'in_app') {
        try {
            const results = [];

            for (const recommendation of recommendations) {
                try {
                    const notification = await this.createNotificationFromRecommendation(
                        recommendation.id,
                        recommendation.user_id,
                        type
                    );
                    results.push({ success: true, notification });
                } catch (error) {
                    results.push({ success: false, error: error.message, recommendation_id: recommendation.id });
                }
            }

            logger.info(`Bulk notifications sent: ${results.filter(r => r.success).length} successful, ${results.filter(r => !r.success).length} failed`);
            return results;

        } catch (error) {
            logger.error('Failed to send bulk notifications:', error);
            throw error;
        }
    }
}

module.exports = new NotificationService();