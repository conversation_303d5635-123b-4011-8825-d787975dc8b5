// AWS SDK v3 implementation
const { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const { TextractClient, AnalyzeDocumentCommand } = require('@aws-sdk/client-textract');
const logger = require('../utils/logger');

// Configure AWS clients
const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'us-east-1',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

const textractClient = new TextractClient({
    region: process.env.AWS_REGION || 'us-east-1',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

// S3 Configuration
const S3_BUCKET = process.env.AWS_S3_BUCKET || 'finance-manager-uploads';

class AWSService {
    constructor() {
        this.s3Client = s3Client;
        this.textractClient = textractClient;
        this.bucket = S3_BUCKET;
    }

    // Upload file to S3
    async uploadFile(buffer, key, contentType) {
        try {
            const command = new PutObjectCommand({
                Bucket: this.bucket,
                Key: key,
                Body: buffer,
                ContentType: contentType,
                ServerSideEncryption: 'AES256'
            });

            const result = await this.s3Client.send(command);
            logger.info(`File uploaded to S3: ${key}`);
            return result;
        } catch (error) {
            logger.error('S3 upload failed:', error);
            throw new Error('File upload failed');
        }
    }

    // Get file from S3
    async getFile(key) {
        try {
            const command = new GetObjectCommand({
                Bucket: this.bucket,
                Key: key
            });

            const result = await this.s3Client.send(command);
            
            // Convert stream to buffer
            const chunks = [];
            for await (const chunk of result.Body) {
                chunks.push(chunk);
            }
            return Buffer.concat(chunks);
        } catch (error) {
            logger.error('S3 download failed:', error);
            throw new Error('File download failed');
        }
    }

    // Delete file from S3
    async deleteFile(key) {
        try {
            const command = new DeleteObjectCommand({
                Bucket: this.bucket,
                Key: key
            });

            await this.s3Client.send(command);
            logger.info(`File deleted from S3: ${key}`);
        } catch (error) {
            logger.error('S3 delete failed:', error);
            throw new Error('File deletion failed');
        }
    }

    // Extract text from PDF using Textract
    async extractTextFromPDF(s3Key) {
        try {
            const command = new AnalyzeDocumentCommand({
                Document: {
                    S3Object: {
                        Bucket: this.bucket,
                        Name: s3Key
                    }
                },
                FeatureTypes: ['TABLES', 'FORMS']
            });

            const result = await this.textractClient.send(command);
            logger.info(`Textract analysis completed for: ${s3Key}`);
            return result;
        } catch (error) {
            logger.error('Textract analysis failed:', error);
            throw new Error('PDF text extraction failed');
        }
    }

    // Generate presigned URL for file access
    async generatePresignedUrl(key, expiresIn = 3600) {
        try {
            const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
            
            const command = new GetObjectCommand({
                Bucket: this.bucket,
                Key: key
            });

            const url = await getSignedUrl(this.s3Client, command, { expiresIn });
            return url;
        } catch (error) {
            logger.error('Presigned URL generation failed:', error);
            throw new Error('URL generation failed');
        }
    }
}

module.exports = new AWSService();