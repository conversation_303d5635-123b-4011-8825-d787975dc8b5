const express = require('express');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const { body, param } = require('express-validator');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const awsService = require('../config/aws');
const ProcessorFactory = require('../processors/processorFactory');
const aiCategorizer = require('../services/aiCategorizer');
const { statementProcessingQueue, aiCategorizationQueue, importQueue } = require('../config/queue');
const logger = require('../utils/logger');
const fileType = require('file-type');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        files: 1
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['text/csv', 'application/csv', 'application/pdf', 'application/x-ofx', 'application/vnd.intu.qfx'];
        const allowedExtensions = ['.csv', '.pdf', '.ofx', '.qfx'];
        
        const hasValidType = allowedTypes.includes(file.mimetype);
        const hasValidExtension = allowedExtensions.some(ext => 
            file.originalname.toLowerCase().endsWith(ext)
        );
        
        if (hasValidType || hasValidExtension) {
            cb(null, true);
        } else {
            cb(new Error('Unsupported file type. Please upload CSV, PDF, OFX, or QFX files.'));
        }
    }
});

// Get supported file formats
router.get('/formats', authenticateToken, (req, res) => {
    const formats = ProcessorFactory.getSupportedFormats();
    res.json({
        supported_formats: formats,
        max_file_size: '10MB',
        max_files: 1
    });
});

// Upload statement file
router.post('/upload', authenticateToken, upload.single('statement'), async (req, res, next) => {
    try {
        const userId = req.userId;
        const user = req.user;
        const file = req.file;

        if (!file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        // Check upload limits for free users - UPDATED TO 1 UPLOAD ONLY
        if (user.subscription_tier === 'free') {
            const currentMonth = new Date();
            const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
            
            const uploadCount = await pool.query(
                'SELECT COUNT(*) FROM statement_uploads WHERE user_id = $1 AND created_at >= $2',
                [userId, startOfMonth]
            );
            
            if (parseInt(uploadCount.rows[0].count) >= 1) {
                return res.status(403).json({
                    error: 'Free users are limited to 1 upload per month',
                    current_uploads: parseInt(uploadCount.rows[0].count),
                    limit: 1,
                    upgrade_url: '/api/user/upgrade-premium'
                });
            }
        }

        // Detect file type
        const detectedType = await fileType.fromBuffer(file.buffer);
        const fileExtension = file.originalname.split('.').pop().toLowerCase();
        
        // Validate file type
        if (!ProcessorFactory.isSupported(fileExtension, file.mimetype)) {
            return res.status(400).json({ 
                error: 'Unsupported file format',
                supported_formats: Object.keys(ProcessorFactory.getSupportedFormats())
            });
        }

        // Generate unique filename and S3 key
        const uploadId = uuidv4();
        const s3Key = `statements/${userId}/${uploadId}/${file.originalname}`;
        
        // Upload to S3
        const s3Result = await awsService.uploadFile(
            file.buffer,
            s3Key,
            file.mimetype
        );

        // Save upload record
        const result = await pool.query(`
            INSERT INTO statement_uploads (
                id, user_id, filename, original_filename, file_type, 
                file_size, s3_key, status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'uploaded')
            RETURNING *
        `, [
            uploadId,
            userId,
            `${uploadId}_${file.originalname}`,
            file.originalname,
            fileExtension,
            file.size,
            s3Key
        ]);

        const upload = result.rows[0];

        // Queue for processing
        await statementProcessingQueue.add('process-statement', {
            uploadId: uploadId,
            userId: userId,
            s3Key: s3Key,
            fileType: fileExtension,
            mimeType: file.mimetype
        }, {
            delay: 1000 // Small delay to ensure DB commit
        });

        logger.info(`Statement uploaded: ${uploadId} for user: ${userId}`);

        res.status(201).json({
            message: 'File uploaded successfully',
            upload: {
                id: upload.id,
                filename: upload.original_filename,
                file_type: upload.file_type,
                file_size: upload.file_size,
                status: upload.status,
                created_at: upload.created_at
            }
        });

    } catch (error) {
        next(error);
    }
});

// Get upload status
router.get('/upload/:uploadId/status', authenticateToken, [
    param('uploadId').isUUID().withMessage('Invalid upload ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const uploadId = req.params.uploadId;

        const result = await pool.query(
            'SELECT * FROM statement_uploads WHERE id = $1 AND user_id = $2',
            [uploadId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Upload not found' });
        }

        const upload = result.rows[0];

        res.json({
            upload: {
                id: upload.id,
                filename: upload.original_filename,
                file_type: upload.file_type,
                status: upload.status,
                total_transactions: upload.total_transactions,
                error_message: upload.error_message,
                created_at: upload.created_at,
                updated_at: upload.updated_at
            }
        });

    } catch (error) {
        next(error);
    }
});

// Get transaction preview
router.get('/upload/:uploadId/preview', authenticateToken, [
    param('uploadId').isUUID().withMessage('Invalid upload ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const uploadId = req.params.uploadId;

        const result = await pool.query(
            'SELECT * FROM statement_uploads WHERE id = $1 AND user_id = $2',
            [uploadId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Upload not found' });
        }

        const upload = result.rows[0];

        if (upload.status !== 'ready' && upload.status !== 'categorized') {
            return res.status(400).json({ 
                error: 'Upload not ready for preview',
                status: upload.status
            });
        }

        if (!upload.processed_data) {
            return res.status(400).json({ error: 'No processed data available' });
        }

        // Get user's categories for mapping
        const categoriesResult = await pool.query(
            'SELECT id, name, color FROM categories WHERE user_id = $1 AND is_active = true',
            [userId]
        );

        const userCategories = categoriesResult.rows;

        res.json({
            upload: {
                id: upload.id,
                filename: upload.original_filename,
                status: upload.status,
                total_transactions: upload.total_transactions
            },
            transactions: upload.processed_data.transactions || [],
            user_categories: userCategories,
            summary: {
                total_count: upload.total_transactions,
                income_count: (upload.processed_data.transactions || []).filter(t => t.type === 'income').length,
                expense_count: (upload.processed_data.transactions || []).filter(t => t.type === 'expense').length,
                total_amount: (upload.processed_data.transactions || []).reduce((sum, t) => sum + Math.abs(t.amount), 0)
            }
        });

    } catch (error) {
        next(error);
    }
});

// Create import session
router.post('/upload/:uploadId/import', authenticateToken, [
    param('uploadId').isUUID().withMessage('Invalid upload ID'),
    body('account_id').isUUID().withMessage('Account ID is required'),
    body('skip_duplicates').optional().isBoolean(),
    body('category_mappings').optional().isObject(),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const uploadId = req.params.uploadId;
        const { account_id, skip_duplicates = true, category_mappings = {} } = req.body;

        // Verify upload exists and is ready
        const uploadResult = await pool.query(
            'SELECT * FROM statement_uploads WHERE id = $1 AND user_id = $2',
            [uploadId, userId]
        );

        if (uploadResult.rows.length === 0) {
            return res.status(404).json({ error: 'Upload not found' });
        }

        const upload = uploadResult.rows[0];

        if (upload.status !== 'ready' && upload.status !== 'categorized') {
            return res.status(400).json({ 
                error: 'Upload not ready for import',
                status: upload.status
            });
        }

        // Verify account belongs to user
        const accountResult = await pool.query(
            'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
            [account_id, userId]
        );

        if (accountResult.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Create import session
        const sessionId = uuidv4();
        const sessionResult = await pool.query(`
            INSERT INTO import_sessions (
                id, upload_id, user_id, account_id, total_transactions, 
                status, settings
            ) VALUES ($1, $2, $3, $4, $5, 'pending', $6)
            RETURNING *
        `, [
            sessionId,
            uploadId,
            userId,
            account_id,
            upload.total_transactions,
            JSON.stringify({
                skip_duplicates,
                category_mappings
            })
        ]);

        const session = sessionResult.rows[0];

        // Queue import job
        await importQueue.add('import-transactions', {
            sessionId: sessionId,
            uploadId: uploadId,
            userId: userId,
            accountId: account_id,
            settings: {
                skip_duplicates,
                category_mappings
            }
        });

        logger.info(`Import session created: ${sessionId} for upload: ${uploadId}`);

        res.status(201).json({
            message: 'Import started successfully',
            session: {
                id: session.id,
                upload_id: session.upload_id,
                account_id: session.account_id,
                total_transactions: session.total_transactions,
                status: session.status,
                created_at: session.created_at
            }
        });

    } catch (error) {
        next(error);
    }
});

// Get import session status
router.get('/import/:sessionId/status', authenticateToken, [
    param('sessionId').isUUID().withMessage('Invalid session ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const sessionId = req.params.sessionId;

        const result = await pool.query(`
            SELECT 
                s.*,
                u.original_filename,
                a.name as account_name
            FROM import_sessions s
            JOIN statement_uploads u ON s.upload_id = u.id
            JOIN accounts a ON s.account_id = a.id
            WHERE s.id = $1 AND s.user_id = $2
        `, [sessionId, userId]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Import session not found' });
        }

        const session = result.rows[0];

        res.json({
            session: {
                id: session.id,
                upload_id: session.upload_id,
                filename: session.original_filename,
                account_name: session.account_name,
                total_transactions: session.total_transactions,
                imported_count: session.imported_count,
                skipped_count: session.skipped_count,
                duplicate_count: session.duplicate_count,
                status: session.status,
                created_at: session.created_at,
                completed_at: session.completed_at,
                progress: session.total_transactions > 0 
                    ? Math.round(((session.imported_count + session.skipped_count) / session.total_transactions) * 100)
                    : 0
            }
        });

    } catch (error) {
        next(error);
    }
});

// Get user's upload history
router.get('/history', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { page = 1, limit = 20 } = req.query;

        const offset = (page - 1) * limit;

        const result = await pool.query(`
            SELECT 
                u.*,
                COUNT(s.id) as import_count
            FROM statement_uploads u
            LEFT JOIN import_sessions s ON u.id = s.upload_id
            WHERE u.user_id = $1
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT $2 OFFSET $3
        `, [userId, limit, offset]);

        const countResult = await pool.query(
            'SELECT COUNT(*) FROM statement_uploads WHERE user_id = $1',
            [userId]
        );

        const uploads = result.rows.map(upload => ({
            id: upload.id,
            filename: upload.original_filename,
            file_type: upload.file_type,
            file_size: upload.file_size,
            status: upload.status,
            total_transactions: upload.total_transactions,
            import_count: parseInt(upload.import_count),
            error_message: upload.error_message,
            created_at: upload.created_at,
            updated_at: upload.updated_at
        }));

        res.json({
            uploads,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(countResult.rows[0].count),
                pages: Math.ceil(countResult.rows[0].count / limit)
            }
        });

    } catch (error) {
        next(error);
    }
});

// Delete upload and associated data
router.delete('/upload/:uploadId', authenticateToken, [
    param('uploadId').isUUID().withMessage('Invalid upload ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const uploadId = req.params.uploadId;

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Get upload details
            const uploadResult = await client.query(
                'SELECT * FROM statement_uploads WHERE id = $1 AND user_id = $2',
                [uploadId, userId]
            );

            if (uploadResult.rows.length === 0) {
                return res.status(404).json({ error: 'Upload not found' });
            }

            const upload = uploadResult.rows[0];

            // Delete from S3
            if (upload.s3_key) {
                try {
                    await awsService.deleteFile(upload.s3_key);
                } catch (error) {
                    logger.warn(`Failed to delete S3 file: ${upload.s3_key}`, error);
                }
            }

            // Delete import sessions
            await client.query(
                'DELETE FROM import_sessions WHERE upload_id = $1',
                [uploadId]
            );

            // Delete upload record
            await client.query(
                'DELETE FROM statement_uploads WHERE id = $1',
                [uploadId]
            );

            await client.query('COMMIT');

            logger.info(`Upload deleted: ${uploadId} for user: ${userId}`);

            res.json({ message: 'Upload deleted successfully' });

        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }

    } catch (error) {
        next(error);
    }
});

module.exports = router;