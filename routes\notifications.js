const express = require('express');
const { body, param } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const notificationService = require('../services/notificationService');
const logger = require('../utils/logger');

const router = express.Router();

// Get all notifications for user
router.get('/', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { type, status, page = 1, limit = 20 } = req.query;

        const offset = (page - 1) * limit;

        let query = `
            SELECT 
                n.*,
                r.title as recommendation_title,
                r.type as recommendation_type
            FROM notifications n
            LEFT JOIN ai_recommendations r ON n.recommendation_id = r.id
            WHERE n.user_id = $1
        `;

        const params = [userId];
        let paramIndex = 2;

        if (type) {
            query += ` AND n.type = $${paramIndex}`;
            params.push(type);
            paramIndex++;
        }

        if (status) {
            query += ` AND n.status = $${paramIndex}`;
            params.push(status);
            paramIndex++;
        }

        query += ` ORDER BY n.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        params.push(limit, offset);

        const result = await pool.query(query, params);

        // Get total count
        let countQuery = 'SELECT COUNT(*) FROM notifications WHERE user_id = $1';
        const countParams = [userId];
        let countParamIndex = 2;

        if (type) {
            countQuery += ` AND type = $${countParamIndex}`;
            countParams.push(type);
            countParamIndex++;
        }

        if (status) {
            countQuery += ` AND status = $${countParamIndex}`;
            countParams.push(status);
        }

        const countResult = await pool.query(countQuery, countParams);
        const total = parseInt(countResult.rows[0].count);

        const notifications = result.rows.map(notification => ({
            ...notification,
            action_data: notification.action_data || {},
            is_read: !!notification.read_at,
            is_clicked: !!notification.clicked_at
        }));

        res.json({
            notifications,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Get unread notifications
router.get('/unread', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const query = `
            SELECT 
                n.*,
                r.title as recommendation_title,
                r.type as recommendation_type
            FROM notifications n
            LEFT JOIN ai_recommendations r ON n.recommendation_id = r.id
            WHERE n.user_id = $1 
            AND n.read_at IS NULL
            AND n.status IN ('sent', 'delivered')
            ORDER BY n.created_at DESC
            LIMIT 50
        `;

        const result = await pool.query(query, [userId]);

        const notifications = result.rows.map(notification => ({
            ...notification,
            action_data: notification.action_data || {}
        }));

        res.json({
            notifications,
            unread_count: notifications.length
        });
    } catch (error) {
        next(error);
    }
});

// Mark notification as read
router.post('/:id/read', authenticateToken, [
    param('id').isUUID().withMessage('Invalid notification ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const notificationId = req.params.id;

        const result = await pool.query(
            'UPDATE notifications SET read_at = NOW(), status = $1 WHERE id = $2 AND user_id = $3 AND read_at IS NULL RETURNING *',
            ['read', notificationId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Notification not found or already read' });
        }

        logger.info(`Notification marked as read: ${notificationId} by user: ${userId}`);

        res.json({
            message: 'Notification marked as read',
            notification: result.rows[0]
        });
    } catch (error) {
        next(error);
    }
});

// Track notification click
router.post('/:id/click', authenticateToken, [
    param('id').isUUID().withMessage('Invalid notification ID'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const notificationId = req.params.id;

        const result = await pool.query(
            'UPDATE notifications SET clicked_at = NOW(), status = $1, read_at = COALESCE(read_at, NOW()) WHERE id = $2 AND user_id = $3 RETURNING *',
            ['clicked', notificationId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Notification not found' });
        }

        logger.info(`Notification clicked: ${notificationId} by user: ${userId}`);

        res.json({
            message: 'Notification click tracked',
            notification: result.rows[0]
        });
    } catch (error) {
        next(error);
    }
});

// Mark all notifications as read
router.post('/mark-all-read', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await pool.query(
            'UPDATE notifications SET read_at = NOW(), status = $1 WHERE user_id = $2 AND read_at IS NULL RETURNING COUNT(*)',
            ['read', userId]
        );

        const updatedCount = result.rowCount;

        logger.info(`Marked ${updatedCount} notifications as read for user: ${userId}`);

        res.json({
            message: `Marked ${updatedCount} notifications as read`,
            updated_count: updatedCount
        });
    } catch (error) {
        next(error);
    }
});

// Get notification preferences
router.get('/preferences', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await pool.query(
            'SELECT * FROM notification_preferences WHERE user_id = $1',
            [userId]
        );

        let preferences;
        if (result.rows.length === 0) {
            // Create default preferences if they don't exist
            await notificationService.createDefaultPreferences(userId);
            preferences = notificationService.getDefaultPreferences();
        } else {
            preferences = result.rows[0];
        }

        res.json({ preferences });
    } catch (error) {
        next(error);
    }
});

// Update notification preferences
router.put('/preferences', authenticateToken, [
    body('push_notifications').optional().isBoolean(),
    body('in_app_notifications').optional().isBoolean(),
    body('email_notifications').optional().isBoolean(),
    body('frequency').optional().isIn(['immediate', 'daily', 'weekly', 'smart']),
    body('quiet_hours_start').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('quiet_hours_end').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('goal_reminders').optional().isBoolean(),
    body('budget_alerts').optional().isBoolean(),
    body('insight_notifications').optional().isBoolean(),
    body('promotional_notifications').optional().isBoolean(),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const {
            push_notifications,
            in_app_notifications,
            email_notifications,
            frequency,
            quiet_hours_start,
            quiet_hours_end,
            goal_reminders,
            budget_alerts,
            insight_notifications,
            promotional_notifications
        } = req.body;

        // Build update query dynamically
        const updateFields = [];
        const updateValues = [userId];
        let paramIndex = 2;

        const fields = {
            push_notifications,
            in_app_notifications,
            email_notifications,
            frequency,
            quiet_hours_start,
            quiet_hours_end,
            goal_reminders,
            budget_alerts,
            insight_notifications,
            promotional_notifications
        };

        Object.entries(fields).forEach(([key, value]) => {
            if (value !== undefined) {
                updateFields.push(`${key} = $${paramIndex}`);
                updateValues.push(value);
                paramIndex++;
            }
        });

        if (updateFields.length === 0) {
            return res.status(400).json({ error: 'No fields to update' });
        }

        updateFields.push('updated_at = NOW()');

        const query = `
            UPDATE notification_preferences 
            SET ${updateFields.join(', ')}
            WHERE user_id = $1
            RETURNING *
        `;

        const result = await pool.query(query, updateValues);

        if (result.rows.length === 0) {
            // Create preferences if they don't exist
            await notificationService.createDefaultPreferences(userId);
            
            // Try update again
            const retryResult = await pool.query(query, updateValues);
            if (retryResult.rows.length === 0) {
                throw new Error('Failed to update notification preferences');
            }
        }

        logger.info(`Notification preferences updated for user: ${userId}`);

        res.json({
            message: 'Notification preferences updated successfully',
            preferences: result.rows[0] || retryResult.rows[0]
        });
    } catch (error) {
        next(error);
    }
});

// Get notification statistics
router.get('/stats', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const statsQuery = `
            SELECT 
                COUNT(*) as total_notifications,
                COUNT(CASE WHEN read_at IS NOT NULL THEN 1 END) as read_notifications,
                COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as clicked_notifications,
                COUNT(CASE WHEN type = 'push' THEN 1 END) as push_notifications,
                COUNT(CASE WHEN type = 'email' THEN 1 END) as email_notifications,
                COUNT(CASE WHEN type = 'in_app' THEN 1 END) as in_app_notifications,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_notifications
            FROM notifications
            WHERE user_id = $1
            AND created_at >= CURRENT_DATE - INTERVAL '30 days'
        `;

        const statsResult = await pool.query(statsQuery, [userId]);
        const stats = statsResult.rows[0];

        // Get recent activity
        const recentQuery = `
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as notification_count,
                COUNT(CASE WHEN read_at IS NOT NULL THEN 1 END) as read_count
            FROM notifications
            WHERE user_id = $1
            AND created_at >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        `;

        const recentResult = await pool.query(recentQuery, [userId]);

        res.json({
            period: '30 days',
            statistics: {
                total_notifications: parseInt(stats.total_notifications),
                read_notifications: parseInt(stats.read_notifications),
                clicked_notifications: parseInt(stats.clicked_notifications),
                read_rate: stats.total_notifications > 0 ? 
                    (parseInt(stats.read_notifications) / parseInt(stats.total_notifications)) * 100 : 0,
                click_rate: stats.total_notifications > 0 ? 
                    (parseInt(stats.clicked_notifications) / parseInt(stats.total_notifications)) * 100 : 0,
                by_type: {
                    push: parseInt(stats.push_notifications),
                    email: parseInt(stats.email_notifications),
                    in_app: parseInt(stats.in_app_notifications)
                },
                failed_notifications: parseInt(stats.failed_notifications)
            },
            recent_activity: recentResult.rows.map(row => ({
                date: row.date,
                notification_count: parseInt(row.notification_count),
                read_count: parseInt(row.read_count)
            }))
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;