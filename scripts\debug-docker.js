const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function debugDocker() {
    console.log('🔍 Docker Debug Information\n');
    
    try {
        // Check Docker version
        console.log('📋 Docker Version:');
        const { stdout: dockerVersion } = await execAsync('docker --version');
        console.log(dockerVersion.trim());
        
        // Check Docker Compose version
        console.log('\n📋 Docker Compose Version:');
        const { stdout: composeVersion } = await execAsync('docker-compose --version');
        console.log(composeVersion.trim());
        
        // Check if Docker daemon is running
        console.log('\n🔧 Docker Daemon Status:');
        try {
            await execAsync('docker info', { timeout: 5000 });
            console.log('✅ Docker daemon is running');
        } catch (error) {
            console.log('❌ Docker daemon is not running or not accessible');
            console.log('   Please start Docker Desktop');
            return;
        }
        
        // Check current containers
        console.log('\n📦 Current Containers:');
        try {
            const { stdout: containers } = await execAsync('docker ps -a --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"');
            console.log(containers || 'No containers found');
        } catch (error) {
            console.log('Unable to list containers');
        }
        
        // Check Docker Compose services
        console.log('\n🐳 Docker Compose Services:');
        try {
            const { stdout: services } = await execAsync('docker-compose ps');
            console.log(services || 'No services found');
        } catch (error) {
            console.log('Unable to list Docker Compose services');
            console.log('This is normal if containers haven\'t been started yet');
        }
        
        // Check port availability
        console.log('\n🔌 Port Availability:');
        const net = require('net');
        
        const checkPort = (port) => {
            return new Promise((resolve) => {
                const server = net.createServer();
                server.listen(port, () => {
                    server.close(() => {
                        resolve(`✅ Port ${port} is available`);
                    });
                });
                server.on('error', () => {
                    resolve(`❌ Port ${port} is in use`);
                });
            });
        };
        
        const port5432 = await checkPort(5432);
        const port6379 = await checkPort(6379);
        console.log(port5432);
        console.log(port6379);
        
        // Check Docker Compose file
        console.log('\n📄 Docker Compose Configuration:');
        try {
            const { stdout: config } = await execAsync('docker-compose config --quiet');
            console.log('✅ docker-compose.yml is valid');
        } catch (error) {
            console.log('❌ docker-compose.yml has issues:');
            console.log(error.message);
        }
        
        console.log('\n🎯 Debug Complete');
        console.log('\n📝 Next Steps:');
        console.log('1. If Docker daemon is not running: Start Docker Desktop');
        console.log('2. If ports are in use: Stop conflicting services');
        console.log('3. Try starting containers: npm run db:start');
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugDocker();